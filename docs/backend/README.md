# Go后端开发文档

## 概述

Social App 的后端使用 Go 语言开发，提供高性能的 RESTful API 服务。

## 技术栈

- **语言**: Go 1.21+
- **Web框架**: Gin
- **数据库**: PostgreSQL
- **ORM**: GORM
- **认证**: JWT
- **缓存**: Redis
- **文件存储**: MinIO
- **容器化**: Docker

## 项目结构

```
backend/
├── cmd/
│   └── main.go              # 应用入口
├── internal/
│   ├── api/                 # API处理器
│   │   ├── server.go       # 服务器配置
│   │   ├── auth.go         # 认证API
│   │   ├── users.go        # 用户API
│   │   ├── posts.go        # 动态API
│   │   ├── notifications.go # 通知API
│   │   └── ai.go           # AI功能API
│   ├── auth/               # 认证相关
│   │   └── jwt.go          # JWT处理
│   ├── config/             # 配置管理
│   │   └── config.go       # 配置结构
│   ├── db/                 # 数据库相关
│   │   └── database.go     # 数据库连接
│   ├── models/             # 数据模型
│   │   └── user.go         # 用户模型
│   ├── services/           # 业务逻辑
│   └── utils/              # 工具函数
├── migrations/             # 数据库迁移
├── docs/                   # API文档
├── tests/                  # 测试文件
├── .env.example           # 环境变量示例
├── Dockerfile             # Docker配置
├── go.mod                 # Go模块
└── go.sum                 # 依赖锁定
```

## 快速开始

### 环境要求

- Go 1.21+
- PostgreSQL 15+
- Redis 7+
- Docker & Docker Compose

### 环境配置

1. 复制环境变量文件：
```bash
cp .env.example .env
```

2. 修改 `.env` 文件中的配置

3. 启动依赖服务：
```bash
docker-compose up -d postgres redis minio
```

### 安装依赖

```bash
go mod download
```

### 运行应用

```bash
go run cmd/main.go
```

服务将在 `http://localhost:8080` 启动

## 核心组件

### 配置管理

配置通过环境变量管理，支持：
- 数据库连接
- JWT密钥
- Redis配置
- MinIO配置
- AI服务配置

### 数据库

使用GORM作为ORM，支持：
- 自动迁移
- 关联查询
- 软删除
- 事务处理

### 认证系统

JWT认证流程：
1. 用户登录获取token
2. 请求头携带token
3. 中间件验证token
4. 提取用户信息

### API路由

路由分组管理：
- `/api/v1/auth/*` - 认证相关（无需token）
- `/api/v1/users/*` - 用户管理（需要token）
- `/api/v1/posts/*` - 动态管理（需要token）
- `/api/v1/notifications/*` - 通知系统（需要token）
- `/api/v1/ai/*` - AI功能（需要token）

## 开发指南

### 添加新API

1. 在对应的处理器文件中添加函数
2. 在 `server.go` 中注册路由
3. 添加必要的中间件
4. 编写测试用例

示例：
```go
// 在 posts.go 中添加处理函数
func (s *Server) getPostStats(c *gin.Context) {
    // 实现逻辑
}

// 在 server.go 中注册路由
postGroup.GET("/:id/stats", s.getPostStats)
```

### 添加数据模型

1. 在 `models/` 目录下定义结构体
2. 添加GORM标签
3. 在 `database.go` 中添加迁移
4. 编写相关的CRUD操作

示例：
```go
type Comment struct {
    ID        uint           `json:"id" gorm:"primaryKey"`
    UserID    uint           `json:"user_id" gorm:"not null"`
    PostID    uint           `json:"post_id" gorm:"not null"`
    Content   string         `json:"content" gorm:"not null"`
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}
```

### 中间件开发

创建自定义中间件：
```go
func CustomMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 前置处理
        c.Next()
        // 后置处理
    }
}
```

### 错误处理

统一错误响应格式：
```go
c.JSON(http.StatusBadRequest, gin.H{
    "error": "错误描述信息",
})
```

## 数据库设计

### 核心表结构

- `users` - 用户信息
- `posts` - 动态内容
- `likes` - 点赞记录
- `comments` - 评论内容
- `follows` - 关注关系
- `notifications` - 通知消息

### 关系设计

- 用户 1:N 动态
- 用户 1:N 点赞
- 用户 1:N 评论
- 用户 M:N 关注关系
- 用户 1:N 通知

### 索引优化

关键索引：
- `users.username` - 唯一索引
- `users.email` - 唯一索引
- `posts.user_id` - 普通索引
- `likes.user_id, post_id` - 复合索引
- `follows.follower_id, following_id` - 复合索引

## 性能优化

### 数据库优化

- 使用连接池
- 预加载关联数据
- 分页查询
- 索引优化
- 查询缓存

### 缓存策略

- 用户信息缓存
- 热门动态缓存
- 计数器缓存
- 会话缓存

### 并发处理

- Goroutine池
- 数据库连接池
- 限流中间件
- 超时控制

## 安全措施

### 输入验证

- 参数类型检查
- 长度限制
- 格式验证
- SQL注入防护

### 认证授权

- JWT token验证
- 权限检查
- 资源访问控制
- 会话管理

### 数据保护

- 密码加密存储
- 敏感信息脱敏
- HTTPS传输
- 数据备份

## 测试

### 单元测试

```bash
go test ./...
```

### 集成测试

```bash
go test -tags=integration ./...
```

### API测试

使用内置的测试工具或Postman进行API测试

## 部署

### Docker部署

```bash
docker build -t social-backend .
docker run -p 8080:8080 social-backend
```

### 生产环境

1. 编译二进制文件
2. 配置环境变量
3. 设置反向代理
4. 配置监控和日志

## 监控和日志

### 日志记录

- 请求日志
- 错误日志
- 性能日志
- 安全日志

### 监控指标

- 响应时间
- 错误率
- 并发数
- 资源使用率

## 常见问题

### 数据库连接问题

1. 检查数据库服务状态
2. 验证连接字符串
3. 检查网络连接
4. 查看数据库日志

### 性能问题

1. 分析慢查询
2. 检查索引使用
3. 优化数据库查询
4. 增加缓存

### 内存泄漏

1. 使用pprof分析
2. 检查goroutine泄漏
3. 优化内存使用
4. 定期重启服务

## 相关文档

- [API文档](../api/README.md)
- [前端开发文档](../frontend/README.md)
- [部署文档](../deployment/README.md)
- [架构设计](../architecture/README.md)

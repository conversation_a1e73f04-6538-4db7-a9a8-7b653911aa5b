# API文档

## 概述

Social App 提供了完整的 RESTful API，支持用户管理、社交功能、AI功能等。

## 基础信息

- **Base URL**: `http://localhost:8080/api/v1`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 快速开始

### 1. 健康检查
```bash
curl http://localhost:8080/api/v1/health
```

### 2. 用户注册
```bash
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "display_name": "Test User"
  }'
```

### 3. 用户登录
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

## 认证

大部分API需要在请求头中包含JWT token：

```
Authorization: Bearer <your_jwt_token>
```

## 错误处理

API使用标准HTTP状态码：

- `200` - 成功
- `201` - 创建成功
- `400` - 请求错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `500` - 服务器错误

错误响应格式：
```json
{
  "error": "错误描述信息"
}
```

## API分类

- [认证相关](./auth.md) - 注册、登录、token管理
- [用户管理](./users.md) - 用户信息、关注、资料管理
- [动态管理](./posts.md) - 发布、获取、点赞、评论
- [通知系统](./notifications.md) - 通知列表、已读状态
- [AI功能](./ai.md) - 智能推荐、内容审核

## 数据模型

详细的数据模型请参考 [数据模型文档](./models.md)

## 测试工具

推荐使用以下工具测试API：
- Postman
- curl
- Flutter应用内置的API测试页面

## 版本信息

当前版本：v1.0.0
更新日志请查看 [CHANGELOG.md](../CHANGELOG.md)

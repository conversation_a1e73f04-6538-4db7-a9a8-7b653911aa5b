# 用户管理API

## 概述

用户管理API提供用户信息获取、资料更新、关注管理等功能。

## 端点列表

### 获取当前用户信息

**GET** `/users/me`

获取当前登录用户的详细信息。

**请求头**
```
Authorization: Bearer <token>
```

**响应**
```json
{
  "user": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "display_name": "Test User",
    "bio": "这是我的个人简介",
    "avatar": "/uploads/avatars/1_avatar.jpg",
    "is_verified": false,
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "follower_count": 10,
  "following_count": 5,
  "post_count": 20
}
```

### 更新用户资料

**PUT** `/users/me`

更新当前用户的个人资料。

**请求头**
```
Authorization: Bearer <token>
```

**请求体**
```json
{
  "display_name": "string (1-50字符)",
  "bio": "string (最多500字符)"
}
```

**响应**
```json
{
  "message": "Profile updated successfully",
  "user": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "display_name": "Updated Name",
    "bio": "Updated bio",
    "avatar": "/uploads/avatars/1_avatar.jpg",
    "is_verified": false,
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 上传头像

**POST** `/users/me/avatar`

上传用户头像图片。

**请求头**
```
Authorization: Bearer <token>
Content-Type: multipart/form-data
```

**请求体**
```
avatar: File (JPEG/PNG/GIF, 最大5MB)
```

**响应**
```json
{
  "message": "Avatar uploaded successfully",
  "avatar_url": "/uploads/avatars/1_new_avatar.jpg"
}
```

**错误响应**
- `400` - 文件类型不支持
- `400` - 文件大小超过限制

### 获取用户资料

**GET** `/users/{id}`

获取指定用户的公开资料信息。

**请求头**
```
Authorization: Bearer <token>
```

**路径参数**
- `id` - 用户ID

**响应**
```json
{
  "user": {
    "id": 2,
    "username": "otheruser",
    "display_name": "Other User",
    "bio": "其他用户的简介",
    "avatar": "/uploads/avatars/2_avatar.jpg",
    "is_verified": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "follower_count": 100,
  "following_count": 50,
  "post_count": 200,
  "is_following": false
}
```

### 关注用户

**POST** `/users/{id}/follow`

关注指定用户。

**请求头**
```
Authorization: Bearer <token>
```

**路径参数**
- `id` - 要关注的用户ID

**响应**
```json
{
  "message": "Successfully followed user"
}
```

**错误响应**
- `400` - 不能关注自己
- `404` - 用户不存在
- `409` - 已经关注该用户

### 取消关注用户

**DELETE** `/users/{id}/follow`

取消关注指定用户。

**请求头**
```
Authorization: Bearer <token>
```

**路径参数**
- `id` - 要取消关注的用户ID

**响应**
```json
{
  "message": "Successfully unfollowed user"
}
```

## 使用示例

### 获取用户信息
```bash
curl -H "Authorization: Bearer <token>" \
  http://localhost:8080/api/v1/users/me
```

### 更新个人资料
```bash
curl -X PUT \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"display_name": "新名称", "bio": "新的个人简介"}' \
  http://localhost:8080/api/v1/users/me
```

### 上传头像
```bash
curl -X POST \
  -H "Authorization: Bearer <token>" \
  -F "avatar=@/path/to/avatar.jpg" \
  http://localhost:8080/api/v1/users/me/avatar
```

### 关注用户
```bash
curl -X POST \
  -H "Authorization: Bearer <token>" \
  http://localhost:8080/api/v1/users/123/follow
```

## 数据字段说明

### User对象
- `id` - 用户唯一标识符
- `username` - 用户名（唯一，3-20字符）
- `email` - 邮箱地址（仅自己可见）
- `display_name` - 显示名称（1-50字符）
- `bio` - 个人简介（最多500字符）
- `avatar` - 头像URL
- `is_verified` - 是否已验证
- `is_active` - 账户是否激活
- `created_at` - 创建时间
- `updated_at` - 更新时间

### 统计信息
- `follower_count` - 关注者数量
- `following_count` - 关注的人数量
- `post_count` - 发布的动态数量
- `is_following` - 当前用户是否关注该用户（仅在查看他人资料时返回）

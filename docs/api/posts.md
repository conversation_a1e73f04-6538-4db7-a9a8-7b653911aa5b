# 动态管理API

## 概述

动态管理API提供动态发布、获取、点赞、评论等社交功能。

## 端点列表

### 获取动态流

**GET** `/posts`

获取用户的动态流，包括关注用户的动态和自己的动态。

**请求头**
```
Authorization: Bearer <token>
```

**查询参数**
- `page` - 页码（默认：1）
- `limit` - 每页数量（默认：20，最大：50）

**响应**
```json
{
  "posts": [
    {
      "id": 1,
      "user_id": 1,
      "content": "这是一条动态内容",
      "images": [
        "/uploads/posts/1_image1.jpg",
        "/uploads/posts/1_image2.jpg"
      ],
      "like_count": 42,
      "is_public": true,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "user": {
        "id": 1,
        "username": "testuser",
        "display_name": "Test User",
        "avatar": "/uploads/avatars/1_avatar.jpg"
      },
      "is_liked": false
    }
  ],
  "page": 1,
  "limit": 20
}
```

### 创建动态

**POST** `/posts`

发布新的动态内容。

**请求头**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**请求体**
```json
{
  "content": "string (1-1000字符)",
  "images": ["string array (可选)"],
  "is_public": "boolean (默认true)"
}
```

**响应**
```json
{
  "message": "Post created successfully",
  "post": {
    "id": 1,
    "user_id": 1,
    "content": "这是一条新动态",
    "images": [],
    "like_count": 0,
    "is_public": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z",
    "user": {
      "id": 1,
      "username": "testuser",
      "display_name": "Test User",
      "avatar": "/uploads/avatars/1_avatar.jpg"
    }
  }
}
```

### 获取动态详情

**GET** `/posts/{id}`

获取指定动态的详细信息，包括评论。

**请求头**
```
Authorization: Bearer <token>
```

**路径参数**
- `id` - 动态ID

**响应**
```json
{
  "id": 1,
  "user_id": 1,
  "content": "这是一条动态内容",
  "images": ["/uploads/posts/1_image1.jpg"],
  "like_count": 42,
  "is_public": true,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z",
  "user": {
    "id": 1,
    "username": "testuser",
    "display_name": "Test User",
    "avatar": "/uploads/avatars/1_avatar.jpg"
  },
  "comments": [
    {
      "id": 1,
      "user_id": 2,
      "post_id": 1,
      "content": "很棒的分享！",
      "created_at": "2024-01-01T01:00:00Z",
      "user": {
        "id": 2,
        "username": "commenter",
        "display_name": "Commenter",
        "avatar": null
      }
    }
  ],
  "is_liked": false
}
```

### 更新动态

**PUT** `/posts/{id}`

更新自己发布的动态内容。

**请求头**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**路径参数**
- `id` - 动态ID

**请求体**
```json
{
  "content": "string (1-1000字符)",
  "images": ["string array (可选)"],
  "is_public": "boolean"
}
```

**响应**
```json
{
  "message": "Post updated successfully",
  "post": {
    "id": 1,
    "content": "更新后的动态内容",
    "images": [],
    "is_public": true,
    "updated_at": "2024-01-01T02:00:00Z"
  }
}
```

### 删除动态

**DELETE** `/posts/{id}`

删除自己发布的动态。

**请求头**
```
Authorization: Bearer <token>
```

**路径参数**
- `id` - 动态ID

**响应**
```json
{
  "message": "Post deleted successfully"
}
```

### 点赞动态

**POST** `/posts/{id}/like`

为动态点赞。

**请求头**
```
Authorization: Bearer <token>
```

**路径参数**
- `id` - 动态ID

**响应**
```json
{
  "message": "Post liked successfully"
}
```

**错误响应**
- `409` - 已经点赞过该动态

### 取消点赞

**DELETE** `/posts/{id}/like`

取消对动态的点赞。

**请求头**
```
Authorization: Bearer <token>
```

**路径参数**
- `id` - 动态ID

**响应**
```json
{
  "message": "Post unliked successfully"
}
```

### 获取评论列表

**GET** `/posts/{id}/comments`

获取动态的评论列表。

**请求头**
```
Authorization: Bearer <token>
```

**路径参数**
- `id` - 动态ID

**查询参数**
- `page` - 页码（默认：1）
- `limit` - 每页数量（默认：20）

**响应**
```json
{
  "comments": [
    {
      "id": 1,
      "user_id": 2,
      "post_id": 1,
      "content": "很棒的分享！",
      "created_at": "2024-01-01T01:00:00Z",
      "updated_at": "2024-01-01T01:00:00Z",
      "user": {
        "id": 2,
        "username": "commenter",
        "display_name": "Commenter",
        "avatar": null
      }
    }
  ],
  "page": 1,
  "limit": 20
}
```

### 创建评论

**POST** `/posts/{id}/comments`

为动态添加评论。

**请求头**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**路径参数**
- `id` - 动态ID

**请求体**
```json
{
  "content": "string (1-500字符)"
}
```

**响应**
```json
{
  "message": "Comment created successfully",
  "comment": {
    "id": 1,
    "user_id": 2,
    "post_id": 1,
    "content": "很棒的分享！",
    "created_at": "2024-01-01T01:00:00Z",
    "updated_at": "2024-01-01T01:00:00Z",
    "user": {
      "id": 2,
      "username": "commenter",
      "display_name": "Commenter",
      "avatar": null
    }
  }
}
```

## 使用示例

### 发布带图片的动态

```bash
# 1. 先上传图片（假设已有图片上传接口）
curl -X POST http://localhost:8080/api/v1/upload \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@image.jpg"

# 2. 创建动态
curl -X POST http://localhost:8080/api/v1/posts \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "分享一张美丽的风景照片！",
    "images": ["/uploads/posts/image.jpg"],
    "is_public": true
  }'
```

### 获取动态流

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:8080/api/v1/posts?page=1&limit=10"
```

### 点赞和评论

```bash
# 点赞
curl -X POST \
  -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8080/api/v1/posts/1/like

# 评论
curl -X POST \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"content": "很棒的分享！"}' \
  http://localhost:8080/api/v1/posts/1/comments
```

## 业务规则

### 权限控制
- 只能编辑/删除自己的动态
- 可以查看公开动态和关注用户的动态
- 私有动态只有作者本人可见

### 内容限制
- 动态内容：1-1000字符
- 评论内容：1-500字符
- 图片数量：最多9张
- 图片大小：单张最大10MB

### 交互限制
- 不能给自己的动态点赞
- 不能重复点赞同一动态
- 删除动态时同时删除相关点赞和评论

## 数据字段说明

### Post对象
- `id` - 动态唯一标识符
- `user_id` - 发布者用户ID
- `content` - 动态文本内容
- `images` - 图片URL数组
- `like_count` - 点赞数量
- `is_public` - 是否公开
- `created_at` - 创建时间
- `updated_at` - 更新时间
- `user` - 发布者用户信息
- `is_liked` - 当前用户是否已点赞

### Comment对象
- `id` - 评论唯一标识符
- `user_id` - 评论者用户ID
- `post_id` - 所属动态ID
- `content` - 评论内容
- `created_at` - 创建时间
- `updated_at` - 更新时间
- `user` - 评论者用户信息

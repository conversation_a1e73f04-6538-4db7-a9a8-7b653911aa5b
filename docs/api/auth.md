# 认证API

## 概述

认证API提供用户注册、登录、token刷新等功能。

## 端点列表

### 用户注册

**POST** `/auth/register`

注册新用户账户。

**请求体**
```json
{
  "username": "string (3-20字符)",
  "email": "string (有效邮箱)",
  "password": "string (最少6字符)",
  "display_name": "string (1-50字符)"
}
```

**响应**
```json
{
  "token": "jwt_token_string",
  "user": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "display_name": "Test User",
    "bio": "",
    "avatar": null,
    "is_verified": false,
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

**错误响应**
- `400` - 请求参数错误
- `409` - 用户名或邮箱已存在

### 用户登录

**POST** `/auth/login`

用户登录获取访问token。

**请求体**
```json
{
  "email": "string",
  "password": "string"
}
```

**响应**
```json
{
  "token": "jwt_token_string",
  "user": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "display_name": "Test User",
    "bio": "",
    "avatar": null,
    "is_verified": false,
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

**错误响应**
- `400` - 请求参数错误
- `401` - 邮箱或密码错误
- `401` - 账户已被停用

### Token刷新

**POST** `/auth/refresh`

刷新访问token。

**请求头**
```
Authorization: Bearer <current_token>
```

**响应**
```json
{
  "token": "new_jwt_token_string"
}
```

**错误响应**
- `401` - Token无效或已过期

## 使用示例

### JavaScript/Fetch
```javascript
// 用户注册
const registerUser = async (userData) => {
  const response = await fetch('/api/v1/auth/register', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(userData),
  });
  
  if (!response.ok) {
    throw new Error('注册失败');
  }
  
  const data = await response.json();
  localStorage.setItem('token', data.token);
  return data;
};

// 用户登录
const loginUser = async (email, password) => {
  const response = await fetch('/api/v1/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ email, password }),
  });
  
  if (!response.ok) {
    throw new Error('登录失败');
  }
  
  const data = await response.json();
  localStorage.setItem('token', data.token);
  return data;
};
```

### Flutter/Dart
```dart
// 使用我们的API服务
final apiService = ref.read(apiServiceProvider);

// 用户注册
final registerRequest = RegisterRequest(
  username: 'testuser',
  email: '<EMAIL>',
  password: 'password123',
  displayName: 'Test User',
);

final authResponse = await apiService.register(registerRequest);

// 用户登录
final loginRequest = LoginRequest(
  email: '<EMAIL>',
  password: 'password123',
);

final authResponse = await apiService.login(loginRequest);
```

## 安全注意事项

1. **密码安全**: 密码在服务器端使用bcrypt加密存储
2. **Token安全**: JWT token包含过期时间，建议定期刷新
3. **HTTPS**: 生产环境必须使用HTTPS传输
4. **输入验证**: 所有输入都经过服务器端验证
5. **频率限制**: 建议实现登录尝试频率限制（待实现）

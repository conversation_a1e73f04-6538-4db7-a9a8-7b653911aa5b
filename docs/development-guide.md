# AI驱动的移动社交APP开发指南

## 项目概述

这是一个基于Go后端和Flutter前端的现代化移动社交应用，集成AI功能提供智能推荐和内容审核。

## 已完成功能

### 后端 (Go)
✅ **基础架构**
- Gin Web框架搭建
- PostgreSQL数据库集成
- JWT认证系统
- CORS中间件配置
- 环境变量管理

✅ **用户认证API**
- 用户注册 `POST /api/v1/auth/register`
- 用户登录 `POST /api/v1/auth/login`
- Token刷新 `POST /api/v1/auth/refresh`

✅ **用户管理API**
- 获取当前用户信息 `GET /api/v1/users/me`
- 更新用户资料 `PUT /api/v1/users/me`
- 头像上传 `POST /api/v1/users/me/avatar`
- 获取用户资料 `GET /api/v1/users/:id`
- 关注/取消关注 `POST/DELETE /api/v1/users/:id/follow`

✅ **动态管理API**
- 获取动态流 `GET /api/v1/posts`
- 创建动态 `POST /api/v1/posts`
- 获取动态详情 `GET /api/v1/posts/:id`
- 更新动态 `PUT /api/v1/posts/:id`
- 删除动态 `DELETE /api/v1/posts/:id`
- 点赞/取消点赞 `POST/DELETE /api/v1/posts/:id/like`
- 评论相关 `GET/POST /api/v1/posts/:id/comments`

✅ **通知系统API**
- 获取通知列表 `GET /api/v1/notifications`
- 标记已读 `PUT /api/v1/notifications/:id/read`

✅ **AI功能API**
- 智能推荐 `POST /api/v1/ai/recommend`
- 内容审核 `POST /api/v1/ai/moderate`

### 前端 (Flutter)
✅ **项目架构**
- Riverpod状态管理
- Go Router路由管理
- 主题系统配置
- 本地存储服务

✅ **认证系统**
- 启动页面
- 登录页面
- 注册页面
- 认证状态管理

✅ **主要界面**
- 主页框架（底部导航）
- 动态流展示
- 用户资料页面
- 发布动态页面

✅ **组件库**
- 动态卡片组件
- 底部导航栏
- 基础UI组件

## 开发环境设置

### 后端开发

1. **启动数据库服务**
```bash
docker-compose up -d postgres redis minio
```

2. **启动Go后端**
```bash
cd backend
go run cmd/main.go
```

3. **测试API**
```bash
# 健康检查
curl http://localhost:8080/api/v1/health

# 用户注册
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>", 
    "password": "password123",
    "display_name": "Test User"
  }'
```

### 前端开发

1. **安装Flutter依赖**
```bash
cd mobile
flutter pub get
```

2. **生成代码**
```bash
flutter packages pub run build_runner build
```

3. **启动应用**
```bash
flutter run
```

## 数据库结构

### 用户表 (users)
- id, username, email, password
- display_name, bio, avatar
- is_verified, is_active
- created_at, updated_at, deleted_at

### 动态表 (posts)
- id, user_id, content, images
- like_count, is_public
- created_at, updated_at, deleted_at

### 点赞表 (likes)
- id, user_id, post_id, created_at

### 评论表 (comments)
- id, user_id, post_id, content
- created_at, updated_at, deleted_at

### 关注表 (follows)
- id, follower_id, following_id, created_at

### 通知表 (notifications)
- id, user_id, type, title, message, data
- is_read, created_at, deleted_at

## API文档

### 认证相关
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh` - 刷新Token

### 用户相关
- `GET /api/v1/users/me` - 获取当前用户信息
- `PUT /api/v1/users/me` - 更新用户资料
- `POST /api/v1/users/me/avatar` - 上传头像
- `GET /api/v1/users/:id` - 获取用户资料
- `POST /api/v1/users/:id/follow` - 关注用户
- `DELETE /api/v1/users/:id/follow` - 取消关注

### 动态相关
- `GET /api/v1/posts` - 获取动态流
- `POST /api/v1/posts` - 创建动态
- `GET /api/v1/posts/:id` - 获取动态详情
- `PUT /api/v1/posts/:id` - 更新动态
- `DELETE /api/v1/posts/:id` - 删除动态
- `POST /api/v1/posts/:id/like` - 点赞动态
- `DELETE /api/v1/posts/:id/like` - 取消点赞

## 下一步开发计划

### 优先级1 - 核心功能完善
- [ ] 完善Flutter前端API集成
- [ ] 实现图片上传功能
- [ ] 完善动态流和交互功能
- [ ] 实现实时通知系统

### 优先级2 - AI功能增强
- [ ] 集成OpenAI API
- [ ] 实现智能内容推荐
- [ ] 内容审核和过滤
- [ ] 智能回复建议

### 优先级3 - 高级功能
- [ ] 实时聊天系统
- [ ] 视频动态支持
- [ ] 地理位置功能
- [ ] 搜索和发现功能

### 优先级4 - 性能和部署
- [ ] 性能优化
- [ ] 单元测试和集成测试
- [ ] CI/CD流水线
- [ ] 生产环境部署

## 技术栈总结

**后端**
- Go + Gin框架
- PostgreSQL数据库
- JWT认证
- Docker容器化

**前端**
- Flutter + Dart
- Riverpod状态管理
- Go Router路由
- Material Design

**AI集成**
- OpenAI API
- 内容审核
- 智能推荐

**部署**
- Docker Compose
- 云服务器部署
- CI/CD自动化

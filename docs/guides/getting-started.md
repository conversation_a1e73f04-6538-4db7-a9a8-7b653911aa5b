# 快速开始指南

## 欢迎使用 Social App

这是一个现代化的AI驱动移动社交应用，使用Go后端和Flutter前端构建。

## 5分钟快速体验

### 第一步：环境准备

确保您的系统已安装：
- [Docker](https://www.docker.com/get-started) 
- [Git](https://git-scm.com/downloads)

### 第二步：获取代码

```bash
git clone <your-repository-url>
cd social
```

### 第三步：启动服务

```bash
# 启动数据库和相关服务
docker-compose up -d postgres redis minio

# 等待服务启动（约30秒）
sleep 30
```

### 第四步：启动后端

```bash
cd backend
cp .env.example .env
go run cmd/main.go
```

看到以下输出表示成功：
```
Server starting on port 8080
```

### 第五步：测试API

打开新终端，测试API：
```bash
curl http://localhost:8080/api/v1/health
```

应该返回：
```json
{"status":"ok","message":"Social API is running"}
```

### 第六步：启动前端（可选）

如果您安装了Flutter：
```bash
cd mobile
flutter pub get
dart run build_runner build
flutter run -d chrome
```

## 核心功能演示

### 1. 用户注册

```bash
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "demo_user",
    "email": "<EMAIL>",
    "password": "password123",
    "display_name": "Demo User"
  }'
```

### 2. 用户登录

```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

保存返回的token，后续请求需要使用。

### 3. 创建动态

```bash
curl -X POST http://localhost:8080/api/v1/posts \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "content": "这是我的第一条动态！",
    "is_public": true
  }'
```

### 4. 获取动态流

```bash
curl -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  http://localhost:8080/api/v1/posts
```

## 项目结构概览

```
social/
├── backend/          # Go后端服务
│   ├── cmd/         # 应用入口
│   ├── internal/    # 内部包
│   └── ...
├── mobile/          # Flutter前端应用
│   ├── lib/         # Dart源代码
│   └── ...
├── docs/            # 项目文档
├── docker-compose.yml
└── README.md
```

## 开发工具推荐

### 后端开发
- **IDE**: VS Code + Go扩展 / GoLand
- **API测试**: Postman / Insomnia
- **数据库**: pgAdmin / DBeaver

### 前端开发
- **IDE**: VS Code + Flutter扩展 / Android Studio
- **调试**: Flutter DevTools
- **设计**: Figma

## 下一步

现在您已经成功运行了Social App，可以：

1. **阅读详细文档**
   - [API文档](../api/README.md) - 了解所有API接口
   - [前端开发](../frontend/README.md) - Flutter开发指南
   - [后端开发](../backend/README.md) - Go开发指南

2. **尝试开发功能**
   - 添加新的API接口
   - 创建新的Flutter页面
   - 实现用户交互功能

3. **部署到生产环境**
   - [部署文档](../deployment/README.md) - 生产环境部署

## 常见问题

### Q: Docker服务启动失败？
A: 检查端口是否被占用，确保5432、6379、9000端口可用。

### Q: Go后端编译失败？
A: 确保Go版本1.21+，运行 `go mod tidy` 更新依赖。

### Q: Flutter运行失败？
A: 确保Flutter版本3.0+，运行 `flutter doctor` 检查环境。

### Q: API请求失败？
A: 检查后端服务是否运行，确认API地址正确。

## 获取帮助

- **文档**: 查看 `docs/` 目录下的详细文档
- **示例**: 查看代码中的注释和示例
- **问题**: 创建GitHub Issue

## 贡献代码

欢迎贡献代码！请：
1. Fork项目
2. 创建功能分支
3. 提交Pull Request

---

🎉 **恭喜！您已经成功启动了Social App！**

现在可以开始探索和开发您的社交应用了。

# 系统架构设计

## 概述

Social App 采用现代化的微服务架构设计，前后端分离，支持高并发和横向扩展。

## 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter Web  │    │  Flutter Mobile │    │   Admin Panel   │
│                 │    │   (iOS/Android) │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Load Balancer │
                    │    (Nginx)      │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API Gateway   │
                    │   (Go Backend)  │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │      Redis      │    │     MinIO       │
│   (主数据库)     │    │     (缓存)      │    │   (文件存储)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 技术选型

### 前端技术栈

| 技术 | 版本 | 用途 | 优势 |
|------|------|------|------|
| Flutter | 3.x | 跨平台UI框架 | 一套代码多端运行 |
| Dart | 3.x | 编程语言 | 类型安全、高性能 |
| Riverpod | 2.x | 状态管理 | 类型安全、测试友好 |
| Go Router | 12.x | 路由管理 | 声明式路由 |
| Dio | 5.x | 网络请求 | 功能丰富、易用 |

### 后端技术栈

| 技术 | 版本 | 用途 | 优势 |
|------|------|------|------|
| Go | 1.21+ | 编程语言 | 高性能、并发友好 |
| Gin | 1.9+ | Web框架 | 轻量级、高性能 |
| GORM | 1.25+ | ORM框架 | 功能丰富、易用 |
| PostgreSQL | 15+ | 关系数据库 | 可靠性高、功能强大 |
| Redis | 7+ | 缓存数据库 | 高性能、丰富数据结构 |
| MinIO | Latest | 对象存储 | S3兼容、自托管 |

## 系统分层

### 前端分层

```
┌─────────────────────────────────────┐
│              UI Layer               │
│        (Screens & Widgets)          │
├─────────────────────────────────────┤
│           Business Layer            │
│         (Providers & Logic)         │
├─────────────────────────────────────┤
│            Data Layer               │
│       (API Service & Models)        │
├─────────────────────────────────────┤
│           Storage Layer             │
│      (Local Storage & Cache)        │
└─────────────────────────────────────┘
```

### 后端分层

```
┌─────────────────────────────────────┐
│           Presentation              │
│         (API Handlers)              │
├─────────────────────────────────────┤
│            Business                 │
│         (Services & Logic)          │
├─────────────────────────────────────┤
│             Data                    │
│        (Models & Repository)        │
├─────────────────────────────────────┤
│          Infrastructure             │
│      (Database & External APIs)     │
└─────────────────────────────────────┘
```

## 数据流设计

### 用户认证流程

```mermaid
sequenceDiagram
    participant U as User
    participant F as Flutter
    participant A as API
    participant D as Database
    participant R as Redis

    U->>F: 输入登录信息
    F->>A: POST /auth/login
    A->>D: 验证用户凭据
    D-->>A: 返回用户信息
    A->>A: 生成JWT Token
    A->>R: 缓存用户会话
    A-->>F: 返回Token和用户信息
    F->>F: 存储Token到本地
    F-->>U: 跳转到主页
```

### 动态发布流程

```mermaid
sequenceDiagram
    participant U as User
    participant F as Flutter
    participant A as API
    participant D as Database
    participant M as MinIO

    U->>F: 创建动态内容
    F->>F: 本地预览
    U->>F: 确认发布
    F->>M: 上传图片文件
    M-->>F: 返回文件URL
    F->>A: POST /posts (包含图片URL)
    A->>D: 保存动态数据
    D-->>A: 返回动态信息
    A-->>F: 返回创建结果
    F-->>U: 显示发布成功
```

## 安全架构

### 认证授权

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   JWT Token     │    │   Middleware    │    │   Permission    │
│                 │    │                 │    │                 │
│ - User ID       │───▶│ - Token验证     │───▶│ - 资源访问控制   │
│ - Permissions   │    │ - 用户信息提取   │    │ - 操作权限检查   │
│ - Expiration    │    │ - 请求日志记录   │    │ - 数据权限过滤   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 数据安全

- **传输安全**: HTTPS/TLS加密
- **存储安全**: 密码bcrypt加密
- **访问控制**: JWT token + 权限检查
- **输入验证**: 参数校验 + SQL注入防护
- **审计日志**: 操作记录 + 异常监控

## 性能设计

### 缓存策略

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Browser Cache │    │   Redis Cache   │    │   Database      │
│                 │    │                 │    │                 │
│ - 静态资源      │    │ - 用户会话      │    │ - 持久化数据     │
│ - API响应       │    │ - 热门内容      │    │ - 关系数据       │
│ - 图片文件      │    │ - 计数器        │    │ - 事务处理       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 数据库优化

- **索引策略**: 主键、外键、查询字段索引
- **查询优化**: 预加载、分页、条件过滤
- **连接池**: 控制并发连接数
- **读写分离**: 主从复制（未来扩展）
- **分库分表**: 水平扩展（未来扩展）

## 扩展性设计

### 水平扩展

```
┌─────────────────┐
│   Load Balancer │
└─────────────────┘
         │
    ┌────┴────┐
    │         │
┌───▼───┐ ┌───▼───┐
│ API-1 │ │ API-2 │
└───────┘ └───────┘
    │         │
    └────┬────┘
         │
┌─────────────────┐
│   Shared DB     │
└─────────────────┘
```

### 微服务拆分（未来规划）

- **用户服务**: 用户管理、认证授权
- **内容服务**: 动态、评论、点赞
- **社交服务**: 关注、推荐、通知
- **媒体服务**: 文件上传、图片处理
- **AI服务**: 内容推荐、智能审核

## 监控体系

### 应用监控

- **性能指标**: 响应时间、吞吐量、错误率
- **业务指标**: 用户活跃度、内容发布量
- **系统指标**: CPU、内存、磁盘、网络
- **日志分析**: 错误日志、访问日志、业务日志

### 告警机制

- **阈值告警**: 性能指标超限
- **异常告警**: 错误率异常
- **业务告警**: 关键业务指标异常
- **基础设施告警**: 服务器资源告警

## 部署架构

### 开发环境

```
┌─────────────────┐
│   Developer     │
│   Machine       │
│                 │
│ - Flutter Web   │
│ - Go Backend    │
│ - Docker Compose│
└─────────────────┘
```

### 生产环境

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CDN           │    │   Load Balancer │    │   Web Servers   │
│                 │    │                 │    │                 │
│ - 静态资源      │    │ - SSL终止       │    │ - Flutter Web   │
│ - 图片文件      │    │ - 负载均衡      │    │ - Nginx         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                    ┌─────────────────┐
                    │   API Servers   │
                    │                 │
                    │ - Go Backend    │
                    │ - Auto Scaling  │
                    └─────────────────┘
                                │
         ┌──────────────────────┼──────────────────────┐
         │                      │                      │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │      Redis      │    │     MinIO       │
│   Cluster       │    │    Cluster      │    │    Cluster      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 技术债务管理

### 代码质量

- **代码审查**: Pull Request流程
- **静态分析**: 代码质量检查
- **测试覆盖**: 单元测试、集成测试
- **文档维护**: API文档、架构文档

### 重构计划

- **性能优化**: 定期性能分析和优化
- **架构演进**: 逐步微服务化
- **技术升级**: 依赖库版本更新
- **安全加固**: 安全漏洞修复

## 相关文档

- [API文档](../api/README.md)
- [前端开发文档](../frontend/README.md)
- [后端开发文档](../backend/README.md)
- [部署文档](../deployment/README.md)

# Flutter前端开发文档

## 概述

Social App 的前端使用 Flutter 开发，支持多平台部署（Web、iOS、Android、桌面）。

## 技术栈

- **框架**: Flutter 3.x
- **语言**: Dart 3.x
- **状态管理**: Riverpod 2.x
- **路由**: Go Router 12.x
- **网络**: Dio + Retrofit
- **本地存储**: Hive + SharedPreferences
- **UI**: Material Design 3

## 项目结构

```
mobile/
├── lib/
│   ├── core/                 # 核心功能
│   │   ├── router/          # 路由配置
│   │   ├── services/        # 服务层
│   │   └── theme/           # 主题配置
│   ├── models/              # 数据模型
│   ├── providers/           # 状态管理
│   ├── screens/             # 页面
│   │   ├── auth/           # 认证页面
│   │   ├── home/           # 主页
│   │   ├── profile/        # 用户资料
│   │   ├── post/           # 动态相关
│   │   └── debug/          # 调试工具
│   ├── widgets/             # 通用组件
│   └── main.dart           # 应用入口
├── test/                    # 测试文件
├── web/                     # Web平台配置
├── assets/                  # 静态资源
└── pubspec.yaml            # 依赖配置
```

## 快速开始

### 环境要求

- Flutter SDK 3.0+
- Dart SDK 3.0+
- Chrome浏览器（Web开发）
- Android Studio / VS Code

### 安装依赖

```bash
cd mobile
flutter pub get
```

### 代码生成

```bash
dart run build_runner build
```

### 运行应用

```bash
# Web平台
flutter run -d chrome

# Android模拟器
flutter run -d android

# iOS模拟器
flutter run -d ios
```

## 核心概念

### 状态管理 (Riverpod)

使用Riverpod进行状态管理，主要Provider包括：

- `authProvider` - 用户认证状态
- `apiServiceProvider` - API服务实例
- `postsProvider` - 动态列表状态
- `userProfileProvider` - 用户资料状态

### 路由管理 (Go Router)

路由配置在 `lib/core/router/app_router.dart`：

- `/splash` - 启动页
- `/login` - 登录页
- `/register` - 注册页
- `/home` - 主页
- `/profile/:userId` - 用户资料页
- `/create-post` - 创建动态页
- `/post/:postId` - 动态详情页

### API服务

API服务使用Retrofit自动生成，配置在 `lib/core/services/api_service.dart`：

- 自动JWT token管理
- 请求/响应拦截器
- 错误处理
- 类型安全的API调用

### 本地存储

使用多种存储方案：

- **SharedPreferences**: 简单键值对（token、用户ID等）
- **Hive**: 复杂对象缓存
- **StorageService**: 统一存储接口

## 开发指南

### 添加新页面

1. 在 `lib/screens/` 下创建页面文件
2. 在 `app_router.dart` 中添加路由
3. 如需状态管理，创建对应的Provider

### 添加新API

1. 在 `api_service.dart` 中添加接口定义
2. 运行代码生成: `dart run build_runner build`
3. 在Provider中调用API
4. 在UI中使用Provider

### 添加新组件

1. 在 `lib/widgets/` 下创建组件文件
2. 遵循Material Design规范
3. 支持主题切换
4. 添加必要的文档注释

## 调试工具

### API测试页面

应用内置了API测试页面，可以：
- 测试所有API端点
- 查看请求/响应日志
- 验证认证流程
- 调试网络问题

访问方式：主页右上角的🐛按钮

### Flutter DevTools

```bash
flutter run --debug
# 然后访问控制台显示的DevTools URL
```

### 日志输出

```dart
import 'dart:developer' as developer;

developer.log('调试信息', name: 'MyApp');
```

## 构建和部署

### Web构建

```bash
flutter build web
```

构建产物在 `build/web/` 目录

### Android构建

```bash
flutter build apk
# 或
flutter build appbundle
```

### iOS构建

```bash
flutter build ios
```

## 最佳实践

### 代码规范

- 使用 `flutter analyze` 检查代码
- 遵循 Dart 官方代码规范
- 使用有意义的变量和函数名
- 添加必要的注释

### 性能优化

- 使用 `const` 构造函数
- 避免在 `build` 方法中创建对象
- 合理使用 `ListView.builder`
- 图片缓存和懒加载

### 错误处理

- 使用 try-catch 处理异常
- 提供用户友好的错误信息
- 记录错误日志
- 实现重试机制

### 测试

- 编写单元测试
- 编写Widget测试
- 使用集成测试验证关键流程

## 常见问题

### 代码生成失败

```bash
# 清理并重新生成
flutter packages pub run build_runner clean
dart run build_runner build --delete-conflicting-outputs
```

### 网络请求失败

1. 检查后端服务是否运行
2. 检查API地址配置
3. 查看网络权限配置
4. 使用API测试页面调试

### 状态管理问题

1. 确保Provider正确配置
2. 检查依赖关系
3. 使用DevTools查看状态变化
4. 避免循环依赖

## 相关文档

- [API文档](../api/README.md)
- [后端开发文档](../backend/README.md)
- [部署文档](../deployment/README.md)
- [架构设计](../architecture/README.md)

# 部署文档

## 概述

本文档描述了 Social App 在不同环境下的部署方案和最佳实践。

## 环境分类

### 开发环境 (Development)
- 本地开发机器
- Docker Compose 一键启动
- 热重载和调试支持

### 测试环境 (Staging)
- 模拟生产环境
- 自动化测试
- 性能测试

### 生产环境 (Production)
- 高可用部署
- 负载均衡
- 监控告警

## 开发环境部署

### 前置要求

- Docker & Docker Compose
- Go 1.21+
- Flutter 3.x
- Git

### 快速启动

1. **克隆项目**
```bash
git clone <repository-url>
cd social
```

2. **启动基础服务**
```bash
docker-compose up -d postgres redis minio
```

3. **启动后端服务**
```bash
cd backend
cp .env.example .env
go run cmd/main.go
```

4. **启动前端应用**
```bash
cd mobile
flutter pub get
dart run build_runner build
flutter run -d chrome
```

### 环境变量配置

创建 `backend/.env` 文件：
```env
PORT=8080
DATABASE_URL=postgres://social_user:social_password@localhost:5432/social_db?sslmode=disable
JWT_SECRET=your-super-secret-jwt-key
REDIS_URL=redis://localhost:6379
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET=social-media
```

## Docker 部署

### 完整 Docker Compose 部署

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./mobile
      dockerfile: Dockerfile.web
    ports:
      - "80:80"
    depends_on:
      - backend
    environment:
      - API_BASE_URL=http://backend:8080

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    depends_on:
      - postgres
      - redis
      - minio
    environment:
      - DATABASE_URL=****************************************************/social_db?sslmode=disable
      - REDIS_URL=redis://redis:6379
      - MINIO_ENDPOINT=minio:9000
    volumes:
      - ./logs:/app/logs

  # 数据库
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: social_db
      POSTGRES_USER: social_user
      POSTGRES_PASSWORD: social_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    ports:
      - "5432:5432"

  # 缓存
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"

  # 对象存储
  minio:
    image: minio/minio:latest
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    command: server /data --console-address ":9001"

  # 反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend

volumes:
  postgres_data:
  redis_data:
  minio_data:
```

### 启动生产环境

```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 云服务部署

### AWS 部署

#### 架构图
```
Internet Gateway
       │
   ALB (Load Balancer)
       │
   ┌───┴───┐
   │       │
ECS Task ECS Task
   │       │
   └───┬───┘
       │
   RDS + ElastiCache + S3
```

#### 部署步骤

1. **创建 ECS 集群**
```bash
aws ecs create-cluster --cluster-name social-app-cluster
```

2. **构建和推送镜像**
```bash
# 后端镜像
docker build -t social-backend ./backend
docker tag social-backend:latest <account>.dkr.ecr.<region>.amazonaws.com/social-backend:latest
docker push <account>.dkr.ecr.<region>.amazonaws.com/social-backend:latest

# 前端镜像
docker build -t social-frontend ./mobile
docker tag social-frontend:latest <account>.dkr.ecr.<region>.amazonaws.com/social-frontend:latest
docker push <account>.dkr.ecr.<region>.amazonaws.com/social-frontend:latest
```

3. **创建任务定义**
```json
{
  "family": "social-app",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "containerDefinitions": [
    {
      "name": "backend",
      "image": "<account>.dkr.ecr.<region>.amazonaws.com/social-backend:latest",
      "portMappings": [
        {
          "containerPort": 8080,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "DATABASE_URL",
          "value": "postgres://..."
        }
      ]
    }
  ]
}
```

### Google Cloud Platform 部署

#### 使用 Cloud Run

1. **部署后端**
```bash
gcloud run deploy social-backend \
  --image gcr.io/PROJECT_ID/social-backend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

2. **部署前端**
```bash
gcloud run deploy social-frontend \
  --image gcr.io/PROJECT_ID/social-frontend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

### 阿里云部署

#### 使用容器服务 ACK

1. **创建集群**
2. **配置镜像仓库**
3. **部署应用**

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: social-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: social-backend
  template:
    metadata:
      labels:
        app: social-backend
    spec:
      containers:
      - name: backend
        image: registry.cn-hangzhou.aliyuncs.com/social/backend:latest
        ports:
        - containerPort: 8080
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: social-secrets
              key: database-url
```

## 监控和日志

### 日志收集

#### 使用 ELK Stack

```yaml
# docker-compose.logging.yml
version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.15.0
    environment:
      - discovery.type=single-node
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

  logstash:
    image: docker.elastic.co/logstash/logstash:7.15.0
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf

  kibana:
    image: docker.elastic.co/kibana/kibana:7.15.0
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch

volumes:
  elasticsearch_data:
```

### 监控指标

#### 使用 Prometheus + Grafana

```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana

volumes:
  grafana_data:
```

## 备份和恢复

### 数据库备份

```bash
# 自动备份脚本
#!/bin/bash
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/social_db_$DATE.sql"

pg_dump -h localhost -U social_user social_db > $BACKUP_FILE
gzip $BACKUP_FILE

# 保留最近30天的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
```

### 文件备份

```bash
# MinIO 数据同步到云存储
mc mirror local/minio-data/ s3/backup-bucket/minio-data/
```

## 安全配置

### SSL/TLS 配置

```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    location / {
        proxy_pass http://frontend:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /api/ {
        proxy_pass http://backend:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 防火墙配置

```bash
# UFW 配置
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw enable
```

## 性能优化

### 数据库优化

```sql
-- 创建索引
CREATE INDEX idx_posts_user_id ON posts(user_id);
CREATE INDEX idx_posts_created_at ON posts(created_at DESC);
CREATE INDEX idx_likes_user_post ON likes(user_id, post_id);

-- 配置参数
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
```

### 缓存配置

```redis
# Redis 配置优化
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接字符串
   - 检查网络连接

2. **内存不足**
   - 增加服务器内存
   - 优化应用内存使用
   - 配置 swap 空间

3. **磁盘空间不足**
   - 清理日志文件
   - 压缩备份文件
   - 扩展磁盘空间

### 健康检查

```bash
# 服务健康检查脚本
#!/bin/bash
curl -f http://localhost:8080/api/v1/health || exit 1
```

## 相关文档

- [架构设计](../architecture/README.md)
- [API文档](../api/README.md)
- [前端开发文档](../frontend/README.md)
- [后端开发文档](../backend/README.md)

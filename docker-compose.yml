version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: social_postgres
    environment:
      POSTGRES_DB: social_db
      POSTGRES_USER: social_user
      POSTGRES_PASSWORD: social_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - social_network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: social_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - social_network

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    container_name: social_minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - social_network

  # Go 后端 API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: social_backend
    environment:
      PORT: 8080
      DATABASE_URL: ****************************************************/social_db?sslmode=disable
      JWT_SECRET: your-super-secret-jwt-key-change-this-in-production
      REDIS_URL: redis://redis:6379
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
      MINIO_BUCKET: social-media
    ports:
      - "8080:8080"
    depends_on:
      - postgres
      - redis
      - minio
    networks:
      - social_network
    volumes:
      - ./backend:/app
    # 开发模式下使用热重载
    command: go run cmd/main.go

volumes:
  postgres_data:
  redis_data:
  minio_data:

networks:
  social_network:
    driver: bridge

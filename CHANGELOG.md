# 更新日志

本文档记录了Social App项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 计划中
- 实时聊天功能
- 视频动态支持
- 推送通知系统
- 移动端原生应用
- 多语言支持

## [1.0.0] - 2024-01-01

### 新增
- 🎉 项目初始版本发布
- ✨ 完整的用户认证系统（注册、登录、JWT）
- ✨ 用户资料管理（头像上传、个人信息编辑）
- ✨ 动态发布和管理系统
- ✨ 社交互动功能（点赞、评论、关注）
- ✨ 通知系统基础架构
- ✨ AI功能接口（智能推荐、内容审核）
- 🏗️ Go后端API服务
- 📱 Flutter Web前端应用
- 🐳 Docker容器化部署
- 📚 完整的项目文档

### 技术特性
- **后端**: Go 1.21 + Gin + PostgreSQL + Redis + MinIO
- **前端**: Flutter 3.x + Riverpod + Go Router + Dio
- **认证**: JWT Token认证机制
- **存储**: PostgreSQL关系数据库 + Redis缓存 + MinIO对象存储
- **部署**: Docker Compose一键部署

### API端点
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh` - Token刷新
- `GET /api/v1/users/me` - 获取当前用户信息
- `PUT /api/v1/users/me` - 更新用户资料
- `POST /api/v1/users/me/avatar` - 上传头像
- `GET /api/v1/users/{id}` - 获取用户资料
- `POST /api/v1/users/{id}/follow` - 关注用户
- `DELETE /api/v1/users/{id}/follow` - 取消关注
- `GET /api/v1/posts` - 获取动态流
- `POST /api/v1/posts` - 创建动态
- `GET /api/v1/posts/{id}` - 获取动态详情
- `PUT /api/v1/posts/{id}` - 更新动态
- `DELETE /api/v1/posts/{id}` - 删除动态
- `POST /api/v1/posts/{id}/like` - 点赞动态
- `DELETE /api/v1/posts/{id}/like` - 取消点赞
- `GET /api/v1/posts/{id}/comments` - 获取评论列表
- `POST /api/v1/posts/{id}/comments` - 创建评论
- `GET /api/v1/notifications` - 获取通知列表
- `PUT /api/v1/notifications/{id}/read` - 标记通知已读
- `POST /api/v1/ai/recommend` - 获取AI推荐
- `POST /api/v1/ai/moderate` - 内容审核

### 数据模型
- **User** - 用户信息表
- **Post** - 动态内容表
- **Like** - 点赞记录表
- **Comment** - 评论内容表
- **Follow** - 关注关系表
- **Notification** - 通知消息表

### 开发工具
- 🐛 内置API测试页面
- 📊 健康检查端点
- 🔧 开发环境Docker配置
- 📝 代码生成工具集成

### 文档
- 📖 [快速开始指南](docs/guides/getting-started.md)
- 📚 [API文档](docs/api/README.md)
- 🏗️ [架构设计文档](docs/architecture/README.md)
- 💻 [前端开发文档](docs/frontend/README.md)
- ⚙️ [后端开发文档](docs/backend/README.md)
- 🚀 [部署文档](docs/deployment/README.md)

### 安全特性
- 🔐 JWT Token认证
- 🛡️ 密码bcrypt加密
- 🚫 SQL注入防护
- ✅ 输入参数验证
- 🔒 CORS跨域配置

### 性能优化
- ⚡ Redis缓存系统
- 📊 数据库索引优化
- 🔄 连接池配置
- 📄 分页查询支持

## [0.1.0] - 2023-12-15

### 新增
- 🎯 项目初始化
- 📋 技术栈选型
- 🏗️ 基础架构设计
- 📝 项目文档框架

---

## 版本说明

### 版本号格式
使用语义化版本号：`主版本号.次版本号.修订号`

- **主版本号**：不兼容的API修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

### 变更类型
- `新增` - 新功能
- `变更` - 对现有功能的变更
- `废弃` - 即将移除的功能
- `移除` - 已移除的功能
- `修复` - 问题修复
- `安全` - 安全相关修复

### 图标说明
- 🎉 重大发布
- ✨ 新功能
- 🏗️ 架构变更
- 📱 前端相关
- ⚙️ 后端相关
- 🐳 部署相关
- 📚 文档更新
- 🐛 调试工具
- 🔐 安全相关
- ⚡ 性能优化
- 🔧 开发工具

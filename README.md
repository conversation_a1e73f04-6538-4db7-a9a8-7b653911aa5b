# AI驱动的移动社交APP

一个基于Go后端和Flutter前端的现代化移动社交应用，集成AI功能提供智能推荐和内容审核。

## 项目架构

```
social/
├── backend/          # Go后端API服务
│   ├── cmd/         # 应用入口
│   ├── internal/    # 内部包
│   │   ├── api/     # API路由和处理器
│   │   ├── auth/    # 认证相关
│   │   ├── config/  # 配置管理
│   │   ├── db/      # 数据库相关
│   │   ├── models/  # 数据模型
│   │   ├── services/# 业务逻辑
│   │   └── utils/   # 工具函数
│   ├── migrations/  # 数据库迁移
│   ├── docs/        # API文档
│   └── tests/       # 测试文件
├── mobile/          # Flutter移动端应用
│   ├── lib/         # Dart源代码
│   │   ├── models/  # 数据模型
│   │   ├── services/# API服务
│   │   ├── screens/ # 页面
│   │   ├── widgets/ # 组件
│   │   └── utils/   # 工具函数
│   ├── assets/      # 静态资源
│   └── test/        # 测试文件
└── docs/            # 项目文档
```

## 技术栈

### 后端 (Go)
- **框架**: Gin Web Framework
- **数据库**: PostgreSQL
- **认证**: JWT
- **文件存储**: MinIO/AWS S3
- **AI集成**: OpenAI API / 本地AI模型
- **缓存**: Redis
- **消息队列**: RabbitMQ

### 前端 (Flutter)
- **状态管理**: Riverpod
- **网络请求**: Dio
- **本地存储**: Hive
- **图片处理**: cached_network_image
- **推送通知**: Firebase Cloud Messaging

## 核心功能

### 用户系统
- [x] 用户注册/登录
- [x] 个人资料管理
- [x] 头像上传
- [x] 隐私设置

### 社交功能
- [x] 发布动态（文字、图片、视频）
- [x] 点赞、评论、分享
- [x] 关注/取消关注
- [x] 私信聊天
- [x] 好友系统

### AI功能
- [x] 智能内容推荐
- [x] 内容审核和过滤
- [x] 智能回复建议
- [x] 情感分析
- [x] 图片内容识别

### 实时功能
- [x] 实时通知
- [x] 在线状态
- [x] 实时聊天
- [x] 动态更新

## 开发环境设置

### 后端环境
```bash
# 安装Go依赖
cd backend
go mod init social-backend
go mod tidy

# 启动开发服务器
go run cmd/main.go
```

### 前端环境
```bash
# 安装Flutter依赖
cd mobile
flutter pub get

# 启动开发服务器
flutter run
```

## API文档

API文档将在开发过程中使用Swagger自动生成，可通过 `/docs` 端点访问。

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

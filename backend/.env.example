# 服务器配置
PORT=8080

# 数据库配置
DATABASE_URL=postgres://username:password@localhost:5432/social_db?sslmode=disable

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Redis配置
REDIS_URL=redis://localhost:6379

# MinIO配置 (文件存储)
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET=social-media

# AI配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-3.5-turbo

# 其他配置
ENVIRONMENT=development
LOG_LEVEL=info

package api

import (
	"net/http"
	"social-backend/internal/models"
	"strconv"

	"github.com/gin-gonic/gin"
)

type UpdateProfileRequest struct {
	DisplayName string `json:"display_name" binding:"required,min=1,max=50"`
	Bio         string `json:"bio" binding:"max=500"`
}

func (s *Server) getCurrentUser(c *gin.Context) {
	userID := c.GetUint("user_id")

	var user models.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// 获取关注者和关注数量
	var followerCount, followingCount int64
	s.db.Model(&models.Follow{}).Where("following_id = ?", userID).Count(&followerCount)
	s.db.Model(&models.Follow{}).Where("follower_id = ?", userID).Count(&followingCount)

	// 获取动态数量
	var postCount int64
	s.db.Model(&models.Post{}).Where("user_id = ?", userID).Count(&postCount)

	response := gin.H{
		"user":            user,
		"follower_count":  followerCount,
		"following_count": followingCount,
		"post_count":      postCount,
	}

	c.JSON(http.StatusOK, response)
}

func (s *Server) updateProfile(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 更新用户信息
	if err := s.db.Model(&models.User{}).Where("id = ?", userID).Updates(models.User{
		DisplayName: req.DisplayName,
		Bio:         req.Bio,
	}).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update profile"})
		return
	}

	// 获取更新后的用户信息
	var user models.User
	s.db.Where("id = ?", userID).First(&user)

	c.JSON(http.StatusOK, gin.H{
		"message": "Profile updated successfully",
		"user":    user,
	})
}

func (s *Server) uploadAvatar(c *gin.Context) {
	userID := c.GetUint("user_id")

	// 处理文件上传
	file, err := c.FormFile("avatar")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No file uploaded"})
		return
	}

	// 验证文件类型
	if file.Header.Get("Content-Type") != "image/jpeg" && 
	   file.Header.Get("Content-Type") != "image/png" && 
	   file.Header.Get("Content-Type") != "image/gif" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file type. Only JPEG, PNG, and GIF are allowed"})
		return
	}

	// 验证文件大小 (5MB)
	if file.Size > 5*1024*1024 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File size too large. Maximum 5MB allowed"})
		return
	}

	// TODO: 实现文件上传到MinIO或云存储
	// 这里暂时返回一个模拟的URL
	avatarURL := "/uploads/avatars/" + strconv.Itoa(int(userID)) + "_" + file.Filename

	// 更新用户头像URL
	if err := s.db.Model(&models.User{}).Where("id = ?", userID).Update("avatar", avatarURL).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update avatar"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":    "Avatar uploaded successfully",
		"avatar_url": avatarURL,
	})
}

func (s *Server) getUserProfile(c *gin.Context) {
	userIDParam := c.Param("id")
	targetUserID, err := strconv.ParseUint(userIDParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	currentUserID := c.GetUint("user_id")

	var user models.User
	if err := s.db.Where("id = ?", targetUserID).First(&user).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// 获取关注者和关注数量
	var followerCount, followingCount int64
	s.db.Model(&models.Follow{}).Where("following_id = ?", targetUserID).Count(&followerCount)
	s.db.Model(&models.Follow{}).Where("follower_id = ?", targetUserID).Count(&followingCount)

	// 获取动态数量
	var postCount int64
	s.db.Model(&models.Post{}).Where("user_id = ?", targetUserID).Count(&postCount)

	// 检查当前用户是否关注了目标用户
	var isFollowing bool
	var follow models.Follow
	if err := s.db.Where("follower_id = ? AND following_id = ?", currentUserID, targetUserID).First(&follow).Error; err == nil {
		isFollowing = true
	}

	response := gin.H{
		"user":            user,
		"follower_count":  followerCount,
		"following_count": followingCount,
		"post_count":      postCount,
		"is_following":    isFollowing,
	}

	c.JSON(http.StatusOK, response)
}

func (s *Server) followUser(c *gin.Context) {
	userIDParam := c.Param("id")
	targetUserID, err := strconv.ParseUint(userIDParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	currentUserID := c.GetUint("user_id")

	// 不能关注自己
	if currentUserID == uint(targetUserID) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot follow yourself"})
		return
	}

	// 检查目标用户是否存在
	var targetUser models.User
	if err := s.db.Where("id = ?", targetUserID).First(&targetUser).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// 检查是否已经关注
	var existingFollow models.Follow
	if err := s.db.Where("follower_id = ? AND following_id = ?", currentUserID, targetUserID).First(&existingFollow).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Already following this user"})
		return
	}

	// 创建关注关系
	follow := models.Follow{
		FollowerID:  currentUserID,
		FollowingID: uint(targetUserID),
	}

	if err := s.db.Create(&follow).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to follow user"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Successfully followed user"})
}

func (s *Server) unfollowUser(c *gin.Context) {
	userIDParam := c.Param("id")
	targetUserID, err := strconv.ParseUint(userIDParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	currentUserID := c.GetUint("user_id")

	// 删除关注关系
	if err := s.db.Where("follower_id = ? AND following_id = ?", currentUserID, targetUserID).Delete(&models.Follow{}).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to unfollow user"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Successfully unfollowed user"})
}

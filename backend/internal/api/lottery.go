package api

import (
	"net/http"
	"social-backend/internal/models"
	"social-backend/internal/services"
	"strconv"

	"github.com/gin-gonic/gin"
)

type CreatePredictionRequest struct {
	LotteryTypeID uint    `json:"lottery_type_id" binding:"required"`
	Period        string  `json:"period" binding:"required"`
	RedBalls      string  `json:"red_balls" binding:"required"`
	BlueBalls     string  `json:"blue_balls"`
	IsPublic      bool    `json:"is_public"`
	Confidence    float64 `json:"confidence"`
}

type AIPredictionRequest struct {
	LotteryTypeID uint   `json:"lottery_type_id" binding:"required"`
	Period        string `json:"period" binding:"required"`
}

// 获取彩票类型列表
func (s *Server) getLotteryTypes(c *gin.Context) {
	lotteryService := services.NewLotteryService(s.db)
	
	lotteryTypes, err := lotteryService.GetLotteryTypes()
	if err != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to fetch lottery types"})
		return
	}

	c.J<PERSON>(http.StatusOK, gin.H{
		"lottery_types": lotteryTypes,
	})
}

// 获取开奖结果
func (s *Server) getDrawResults(c *gin.Context) {
	lotteryTypeIDParam := c.Param("lottery_type_id")
	lotteryTypeID, err := strconv.ParseUint(lotteryTypeIDParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lottery type ID"})
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if limit > 100 {
		limit = 100
	}

	lotteryService := services.NewLotteryService(s.db)
	results, err := lotteryService.GetDrawResults(uint(lotteryTypeID), limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch draw results"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"draw_results": results,
		"limit":       limit,
	})
}

// 创建预测
func (s *Server) createPrediction(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req CreatePredictionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	lotteryService := services.NewLotteryService(s.db)
	prediction, err := lotteryService.CreatePrediction(
		userID,
		req.LotteryTypeID,
		req.Period,
		req.RedBalls,
		req.BlueBalls,
		"manual",
		req.Confidence,
		req.IsPublic,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create prediction"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":    "Prediction created successfully",
		"prediction": prediction,
	})
}

// 获取用户预测列表
func (s *Server) getUserPredictions(c *gin.Context) {
	userID := c.GetUint("user_id")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if limit > 50 {
		limit = 50
	}

	lotteryService := services.NewLotteryService(s.db)
	predictions, err := lotteryService.GetUserPredictions(userID, page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch predictions"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"predictions": predictions,
		"page":        page,
		"limit":       limit,
	})
}

// 获取公开预测列表
func (s *Server) getPublicPredictions(c *gin.Context) {
	lotteryTypeID, _ := strconv.ParseUint(c.DefaultQuery("lottery_type_id", "0"), 10, 32)
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if limit > 50 {
		limit = 50
	}

	lotteryService := services.NewLotteryService(s.db)
	predictions, err := lotteryService.GetPublicPredictions(uint(lotteryTypeID), page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch public predictions"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"predictions": predictions,
		"page":        page,
		"limit":       limit,
	})
}

// 生成AI预测
func (s *Server) generateAIPrediction(c *gin.Context) {
	var req AIPredictionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	lotteryService := services.NewLotteryService(s.db)
	prediction, err := lotteryService.GenerateAIPrediction(req.LotteryTypeID, req.Period)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate AI prediction"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":    "AI prediction generated successfully",
		"prediction": prediction,
	})
}

// 点赞预测
func (s *Server) likePrediction(c *gin.Context) {
	predictionIDParam := c.Param("prediction_id")
	predictionID, err := strconv.ParseUint(predictionIDParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid prediction ID"})
		return
	}

	userID := c.GetUint("user_id")

	lotteryService := services.NewLotteryService(s.db)
	if err := lotteryService.LikePrediction(userID, uint(predictionID)); err != nil {
		if err.Error() == "already liked this prediction" {
			c.JSON(http.StatusConflict, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to like prediction"})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Prediction liked successfully"})
}

// 取消点赞预测
func (s *Server) unlikePrediction(c *gin.Context) {
	predictionIDParam := c.Param("prediction_id")
	predictionID, err := strconv.ParseUint(predictionIDParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid prediction ID"})
		return
	}

	userID := c.GetUint("user_id")

	lotteryService := services.NewLotteryService(s.db)
	if err := lotteryService.UnlikePrediction(userID, uint(predictionID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to unlike prediction"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Prediction unliked successfully"})
}

// 获取预测详情
func (s *Server) getPredictionDetail(c *gin.Context) {
	predictionIDParam := c.Param("prediction_id")
	predictionID, err := strconv.ParseUint(predictionIDParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid prediction ID"})
		return
	}

	var prediction models.Prediction
	if err := s.db.Preload("User").Preload("LotteryType").
		Where("id = ?", predictionID).First(&prediction).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Prediction not found"})
		return
	}

	// 检查权限（私有预测只有作者可见）
	userID := c.GetUint("user_id")
	if !prediction.IsPublic && prediction.UserID != userID {
		c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
		return
	}

	// 清除敏感信息
	prediction.User.Password = ""

	c.JSON(http.StatusOK, prediction)
}

// 初始化彩票数据
func (s *Server) initializeLotteryData(c *gin.Context) {
	lotteryService := services.NewLotteryService(s.db)
	
	if err := lotteryService.InitializeLotteryTypes(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initialize lottery data"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Lottery data initialized successfully"})
}

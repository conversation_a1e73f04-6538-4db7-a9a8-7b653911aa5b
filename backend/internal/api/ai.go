package api

import (
	"net/http"
	"social-backend/internal/models"

	"github.com/gin-gonic/gin"
)

type RecommendationRequest struct {
	Type  string `json:"type" binding:"required"` // "posts", "users", "topics"
	Limit int    `json:"limit" binding:"min=1,max=50"`
}

type ModerationRequest struct {
	Content string `json:"content" binding:"required"`
	Type    string `json:"type" binding:"required"` // "post", "comment", "profile"
}

type RecommendationResponse struct {
	Type  string      `json:"type"`
	Items interface{} `json:"items"`
	Score float64     `json:"score,omitempty"`
}

type ModerationResponse struct {
	IsAppropriate bool    `json:"is_appropriate"`
	Confidence    float64 `json:"confidence"`
	Reason        string  `json:"reason,omitempty"`
	Suggestions   string  `json:"suggestions,omitempty"`
}

func (s *Server) getRecommendations(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req RecommendationRequest
	if err := c.ShouldBind<PERSON>N(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if req.Limit == 0 {
		req.Limit = 10
	}

	var response RecommendationResponse

	switch req.Type {
	case "posts":
		response = s.getPostRecommendations(userID, req.Limit)
	case "users":
		response = s.getUserRecommendations(userID, req.Limit)
	case "topics":
		response = s.getTopicRecommendations(userID, req.Limit)
	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid recommendation type"})
		return
	}

	c.JSON(http.StatusOK, response)
}

func (s *Server) getPostRecommendations(userID uint, limit int) RecommendationResponse {
	// 基于用户行为的简单推荐算法
	// 1. 获取用户关注的人的动态
	// 2. 获取用户点赞过的动态的相似动态
	// 3. 获取热门动态

	var posts []models.Post

	// 获取热门动态（点赞数高的）
	s.db.Preload("User").
		Where("is_public = ? AND like_count > ?", true, 5).
		Where("user_id NOT IN (SELECT following_id FROM follows WHERE follower_id = ?)", userID).
		Order("like_count DESC, created_at DESC").
		Limit(limit).
		Find(&posts)

	// 清除密码字段
	for i := range posts {
		posts[i].User.Password = ""
	}

	return RecommendationResponse{
		Type:  "posts",
		Items: posts,
		Score: 0.8, // 模拟推荐分数
	}
}

func (s *Server) getUserRecommendations(userID uint, limit int) RecommendationResponse {
	// 推荐用户算法
	// 1. 共同关注的朋友
	// 2. 活跃用户
	// 3. 相似兴趣的用户

	var users []models.User

	// 获取活跃用户（发布动态多的用户）
	s.db.Table("users").
		Select("users.*, COUNT(posts.id) as post_count").
		Joins("LEFT JOIN posts ON users.id = posts.user_id").
		Where("users.id != ?", userID).
		Where("users.id NOT IN (SELECT following_id FROM follows WHERE follower_id = ?)", userID).
		Group("users.id").
		Having("COUNT(posts.id) > ?", 3).
		Order("post_count DESC").
		Limit(limit).
		Find(&users)

	// 清除密码字段
	for i := range users {
		users[i].Password = ""
	}

	return RecommendationResponse{
		Type:  "users",
		Items: users,
		Score: 0.7,
	}
}

func (s *Server) getTopicRecommendations(userID uint, limit int) RecommendationResponse {
	// 话题推荐（基于用户互动的内容关键词）
	topics := []string{
		"科技", "旅行", "美食", "音乐", "电影", 
		"读书", "健身", "摄影", "艺术", "游戏",
	}

	// 这里可以基于用户的历史行为来个性化推荐
	// 暂时返回热门话题

	if limit > len(topics) {
		limit = len(topics)
	}

	return RecommendationResponse{
		Type:  "topics",
		Items: topics[:limit],
		Score: 0.6,
	}
}

func (s *Server) moderateContent(c *gin.Context) {
	var req ModerationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 简单的内容审核逻辑
	// 在实际应用中，这里会调用AI服务进行内容审核
	response := s.performContentModeration(req.Content, req.Type)

	c.JSON(http.StatusOK, response)
}

func (s *Server) performContentModeration(content, contentType string) ModerationResponse {
	// 简单的关键词过滤
	inappropriateWords := []string{
		"垃圾", "广告", "spam", "恶意", "诈骗",
	}

	isAppropriate := true
	reason := ""
	confidence := 0.9

	for _, word := range inappropriateWords {
		if contains(content, word) {
			isAppropriate = false
			reason = "包含不当内容: " + word
			confidence = 0.95
			break
		}
	}

	// 检查内容长度
	if len(content) < 5 {
		isAppropriate = false
		reason = "内容过短，可能是垃圾信息"
		confidence = 0.7
	}

	suggestions := ""
	if !isAppropriate {
		suggestions = "请修改内容，确保符合社区规范"
	}

	return ModerationResponse{
		IsAppropriate: isAppropriate,
		Confidence:    confidence,
		Reason:        reason,
		Suggestions:   suggestions,
	}
}

// 辅助函数：检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || 
		    contains(s[1:], substr) || 
		    (len(s) > 0 && s[:len(s)-1] != s && contains(s[:len(s)-1], substr)))
}

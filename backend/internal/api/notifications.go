package api

import (
	"net/http"
	"social-backend/internal/models"
	"strconv"

	"github.com/gin-gonic/gin"
)

func (s *Server) getNotifications(c *gin.Context) {
	userID := c.GetUint("user_id")
	page, _ := strconv.Atoi(c<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c<PERSON>("limit", "20"))
	offset := (page - 1) * limit

	var notifications []models.Notification
	if err := s.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&notifications).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch notifications"})
		return
	}

	// 获取未读通知数量
	var unreadCount int64
	s.db.Model(&models.Notification{}).Where("user_id = ? AND is_read = ?", userID, false).Count(&unreadCount)

	c.<PERSON>(http.StatusOK, gin.H{
		"notifications": notifications,
		"unread_count":  unreadCount,
		"page":          page,
		"limit":         limit,
	})
}

func (s *Server) markNotificationAsRead(c *gin.Context) {
	notificationIDParam := c.Param("id")
	notificationID, err := strconv.ParseUint(notificationIDParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid notification ID"})
		return
	}

	userID := c.GetUint("user_id")

	// 更新通知为已读
	if err := s.db.Model(&models.Notification{}).
		Where("id = ? AND user_id = ?", notificationID, userID).
		Update("is_read", true).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to mark notification as read"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Notification marked as read"})
}

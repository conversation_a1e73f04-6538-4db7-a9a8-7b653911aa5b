package api

import (
	"net/http"
	"social-backend/internal/models"
	"strconv"

	"github.com/gin-gonic/gin"
)

type CreatePostRequest struct {
	Content  string   `json:"content" binding:"required,min=1,max=1000"`
	Images   []string `json:"images"`
	IsPublic bool     `json:"is_public"`
}

type CreateCommentRequest struct {
	Content string `json:"content" binding:"required,min=1,max=500"`
}

func (s *Server) getFeed(c *gin.Context) {
	userID := c.GetUint("user_id")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>fault<PERSON>("limit", "20"))
	offset := (page - 1) * limit

	var posts []models.Post

	// 获取关注用户的动态和自己的动态
	query := s.db.Preload("User").Preload("Likes").Preload("Comments").
		Where("user_id = ? OR user_id IN (SELECT following_id FROM follows WHERE follower_id = ?)", userID, userID).
		Where("is_public = ?", true).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset)

	if err := query.Find(&posts).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch feed"})
		return
	}

	// 为每个动态添加额外信息
	for i := range posts {
		posts[i].LikeCount = len(posts[i].Likes)

		// 添加自定义字段
		posts[i].User.Password = "" // 清除密码字段
	}

	c.JSON(http.StatusOK, gin.H{
		"posts": posts,
		"page":  page,
		"limit": limit,
	})
}

func (s *Server) createPost(c *gin.Context) {
	userID := c.GetUint("user_id")

	var req CreatePostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	post := models.Post{
		UserID:   userID,
		Content:  req.Content,
		Images:   req.Images,
		IsPublic: req.IsPublic,
	}

	if err := s.db.Create(&post).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create post"})
		return
	}

	// 预加载用户信息
	s.db.Preload("User").First(&post, post.ID)
	post.User.Password = ""

	c.JSON(http.StatusCreated, gin.H{
		"message": "Post created successfully",
		"post":    post,
	})
}

func (s *Server) getPost(c *gin.Context) {
	postIDParam := c.Param("id")
	postID, err := strconv.ParseUint(postIDParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid post ID"})
		return
	}

	var post models.Post
	if err := s.db.Preload("User").Preload("Likes").Preload("Comments.User").
		Where("id = ?", postID).First(&post).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Post not found"})
		return
	}

	// 清除密码字段
	post.User.Password = ""
	for i := range post.Comments {
		post.Comments[i].User.Password = ""
	}

	c.JSON(http.StatusOK, post)
}

func (s *Server) updatePost(c *gin.Context) {
	postIDParam := c.Param("id")
	postID, err := strconv.ParseUint(postIDParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid post ID"})
		return
	}

	userID := c.GetUint("user_id")

	var req CreatePostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查动态是否存在且属于当前用户
	var post models.Post
	if err := s.db.Where("id = ? AND user_id = ?", postID, userID).First(&post).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Post not found or access denied"})
		return
	}

	// 更新动态
	if err := s.db.Model(&post).Updates(models.Post{
		Content:  req.Content,
		Images:   req.Images,
		IsPublic: req.IsPublic,
	}).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update post"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Post updated successfully",
		"post":    post,
	})
}

func (s *Server) deletePost(c *gin.Context) {
	postIDParam := c.Param("id")
	postID, err := strconv.ParseUint(postIDParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid post ID"})
		return
	}

	userID := c.GetUint("user_id")

	// 检查动态是否存在且属于当前用户
	var post models.Post
	if err := s.db.Where("id = ? AND user_id = ?", postID, userID).First(&post).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Post not found or access denied"})
		return
	}

	// 软删除动态
	if err := s.db.Delete(&post).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete post"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Post deleted successfully"})
}

func (s *Server) likePost(c *gin.Context) {
	postIDParam := c.Param("id")
	postID, err := strconv.ParseUint(postIDParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid post ID"})
		return
	}

	userID := c.GetUint("user_id")

	// 检查动态是否存在
	var post models.Post
	if err := s.db.Where("id = ?", postID).First(&post).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Post not found"})
		return
	}

	// 检查是否已经点赞
	var existingLike models.Like
	if err := s.db.Where("user_id = ? AND post_id = ?", userID, postID).First(&existingLike).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Already liked this post"})
		return
	}

	// 创建点赞记录
	like := models.Like{
		UserID: userID,
		PostID: uint(postID),
	}

	if err := s.db.Create(&like).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to like post"})
		return
	}

	// 更新动态点赞数
	s.db.Model(&post).Update("like_count", post.LikeCount+1)

	c.JSON(http.StatusOK, gin.H{"message": "Post liked successfully"})
}

func (s *Server) unlikePost(c *gin.Context) {
	postIDParam := c.Param("id")
	postID, err := strconv.ParseUint(postIDParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid post ID"})
		return
	}

	userID := c.GetUint("user_id")

	// 删除点赞记录
	if err := s.db.Where("user_id = ? AND post_id = ?", userID, postID).Delete(&models.Like{}).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to unlike post"})
		return
	}

	// 更新动态点赞数
	var post models.Post
	if err := s.db.Where("id = ?", postID).First(&post).Error; err == nil {
		if post.LikeCount > 0 {
			s.db.Model(&post).Update("like_count", post.LikeCount-1)
		}
	}

	c.JSON(http.StatusOK, gin.H{"message": "Post unliked successfully"})
}

func (s *Server) getComments(c *gin.Context) {
	postIDParam := c.Param("id")
	postID, err := strconv.ParseUint(postIDParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid post ID"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset := (page - 1) * limit

	var comments []models.Comment
	if err := s.db.Preload("User").Where("post_id = ?", postID).
		Order("created_at ASC").Limit(limit).Offset(offset).Find(&comments).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch comments"})
		return
	}

	// 清除密码字段
	for i := range comments {
		comments[i].User.Password = ""
	}

	c.JSON(http.StatusOK, gin.H{
		"comments": comments,
		"page":     page,
		"limit":    limit,
	})
}

func (s *Server) createComment(c *gin.Context) {
	postIDParam := c.Param("id")
	postID, err := strconv.ParseUint(postIDParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid post ID"})
		return
	}

	userID := c.GetUint("user_id")

	var req CreateCommentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查动态是否存在
	var post models.Post
	if err := s.db.Where("id = ?", postID).First(&post).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Post not found"})
		return
	}

	comment := models.Comment{
		UserID:  userID,
		PostID:  uint(postID),
		Content: req.Content,
	}

	if err := s.db.Create(&comment).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create comment"})
		return
	}

	// 预加载用户信息
	s.db.Preload("User").First(&comment, comment.ID)
	comment.User.Password = ""

	c.JSON(http.StatusCreated, gin.H{
		"message": "Comment created successfully",
		"comment": comment,
	})
}

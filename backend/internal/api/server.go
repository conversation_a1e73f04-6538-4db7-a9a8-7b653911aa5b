package api

import (
	"social-backend/internal/auth"
	"social-backend/internal/config"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Server struct {
	config *config.Config
	db     *gorm.DB
	router *gin.Engine
}

func NewServer(cfg *config.Config, db *gorm.DB) *Server {
	server := &Server{
		config: cfg,
		db:     db,
		router: gin.Default(),
	}

	server.setupMiddleware()
	server.setupRoutes()

	return server
}

func (s *Server) setupMiddleware() {
	// CORS配置
	config := cors.DefaultConfig()
	config.AllowAllOrigins = true
	config.AllowHeaders = []string{"Origin", "Content-Length", "Content-Type", "Authorization"}
	s.router.Use(cors.New(config))

	// 日志中间件
	s.router.Use(gin.Logger())
	s.router.Use(gin.Recovery())
}

func (s *Server) setupRoutes() {
	api := s.router.Group("/api/v1")

	// 健康检查
	api.GET("/health", s.healthCheck)

	// 认证路由
	authGroup := api.Group("/auth")
	{
		authGroup.POST("/register", s.register)
		authGroup.POST("/login", s.login)
		authGroup.POST("/refresh", s.refreshToken)
	}

	// 需要认证的路由
	protected := api.Group("/")
	protected.Use(auth.JWTMiddleware(s.config.JWTSecret))
	{
		// 用户相关
		userGroup := protected.Group("/users")
		{
			userGroup.GET("/me", s.getCurrentUser)
			userGroup.PUT("/me", s.updateProfile)
			userGroup.POST("/me/avatar", s.uploadAvatar)
			userGroup.GET("/:id", s.getUserProfile)
			userGroup.POST("/:id/follow", s.followUser)
			userGroup.DELETE("/:id/follow", s.unfollowUser)
		}

		// 动态相关
		postGroup := protected.Group("/posts")
		{
			postGroup.GET("/", s.getFeed)
			postGroup.POST("/", s.createPost)
			postGroup.GET("/:id", s.getPost)
			postGroup.PUT("/:id", s.updatePost)
			postGroup.DELETE("/:id", s.deletePost)
			postGroup.POST("/:id/like", s.likePost)
			postGroup.DELETE("/:id/like", s.unlikePost)
			postGroup.GET("/:id/comments", s.getComments)
			postGroup.POST("/:id/comments", s.createComment)
		}

		// 通知相关
		notificationGroup := protected.Group("/notifications")
		{
			notificationGroup.GET("/", s.getNotifications)
			notificationGroup.PUT("/:id/read", s.markNotificationAsRead)
		}

		// AI功能
		aiGroup := protected.Group("/ai")
		{
			aiGroup.POST("/recommend", s.getRecommendations)
			aiGroup.POST("/moderate", s.moderateContent)
		}
	}
}

func (s *Server) Start() error {
	return s.router.Run(":" + s.config.Port)
}

// 健康检查
func (s *Server) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"message": "Social API is running",
	})
}

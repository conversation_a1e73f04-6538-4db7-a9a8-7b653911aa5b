package services

import (
	"fmt"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"time"

	"social-backend/internal/models"

	"gorm.io/gorm"
)

type LotteryService struct {
	db *gorm.DB
}

func NewLotteryService(db *gorm.DB) *LotteryService {
	return &LotteryService{db: db}
}

// InitializeLotteryTypes 初始化彩票类型
func (s *LotteryService) InitializeLotteryTypes() error {
	lotteryTypes := []models.LotteryType{
		{
			Name:        "双色球",
			Code:        "ssq",
			Description: "中国福利彩票双色球",
			RedBalls:    6,
			BlueBalls:   1,
			RedRange:    "1-33",
			BlueRange:   "1-16",
			DrawDays:    "2,4,7", // 周二、四、日
			IsActive:    true,
		},
		{
			Name:        "大乐透",
			Code:        "dlt",
			Description: "体彩超级大乐透",
			RedBalls:    5,
			BlueBalls:   2,
			RedRange:    "1-35",
			BlueRange:   "1-12",
			DrawDays:    "1,3,6", // 周一、三、六
			IsActive:    true,
		},
		{
			Name:        "福彩3D",
			Code:        "fc3d",
			Description: "福彩3D",
			RedBalls:    3,
			BlueBalls:   0,
			RedRange:    "0-9",
			BlueRange:   "",
			DrawDays:    "1,2,3,4,5,6,7", // 每天
			IsActive:    true,
		},
	}

	for _, lotteryType := range lotteryTypes {
		var existing models.LotteryType
		if err := s.db.Where("code = ?", lotteryType.Code).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := s.db.Create(&lotteryType).Error; err != nil {
					return err
				}
			} else {
				return err
			}
		}
	}

	return nil
}

// GetLotteryTypes 获取彩票类型列表
func (s *LotteryService) GetLotteryTypes() ([]models.LotteryType, error) {
	var lotteryTypes []models.LotteryType
	err := s.db.Where("is_active = ?", true).Find(&lotteryTypes).Error
	return lotteryTypes, err
}

// GetDrawResults 获取开奖结果
func (s *LotteryService) GetDrawResults(lotteryTypeID uint, limit int) ([]models.DrawResult, error) {
	var results []models.DrawResult
	err := s.db.Where("lottery_type_id = ?", lotteryTypeID).
		Order("draw_date DESC").
		Limit(limit).
		Preload("LotteryType").
		Find(&results).Error
	return results, err
}

// CreatePrediction 创建预测
func (s *LotteryService) CreatePrediction(userID, lotteryTypeID uint, period, redBalls, blueBalls, method string, confidence float64, isPublic bool) (*models.Prediction, error) {
	prediction := &models.Prediction{
		UserID:        userID,
		LotteryTypeID: lotteryTypeID,
		Period:        period,
		RedBalls:      redBalls,
		BlueBalls:     blueBalls,
		Confidence:    confidence,
		Method:        method,
		IsPublic:      isPublic,
	}

	if err := s.db.Create(prediction).Error; err != nil {
		return nil, err
	}

	// 预加载关联数据
	s.db.Preload("User").Preload("LotteryType").First(prediction, prediction.ID)
	return prediction, nil
}

// GetUserPredictions 获取用户预测列表
func (s *LotteryService) GetUserPredictions(userID uint, page, limit int) ([]models.Prediction, error) {
	var predictions []models.Prediction
	offset := (page - 1) * limit

	err := s.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Preload("LotteryType").
		Find(&predictions).Error

	return predictions, err
}

// GetPublicPredictions 获取公开预测列表
func (s *LotteryService) GetPublicPredictions(lotteryTypeID uint, page, limit int) ([]models.Prediction, error) {
	var predictions []models.Prediction
	offset := (page - 1) * limit

	query := s.db.Where("is_public = ?", true)
	if lotteryTypeID > 0 {
		query = query.Where("lottery_type_id = ?", lotteryTypeID)
	}

	err := query.Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Preload("User").
		Preload("LotteryType").
		Find(&predictions).Error

	return predictions, err
}

// GenerateAIPrediction 生成AI预测
func (s *LotteryService) GenerateAIPrediction(lotteryTypeID uint, period string) (*models.Prediction, error) {
	// 获取彩票类型信息
	var lotteryType models.LotteryType
	if err := s.db.First(&lotteryType, lotteryTypeID).Error; err != nil {
		return nil, err
	}

	// 获取历史数据进行分析
	var historicalResults []models.DrawResult
	s.db.Where("lottery_type_id = ?", lotteryTypeID).
		Order("draw_date DESC").
		Limit(100).
		Find(&historicalResults)

	// 简单的AI预测算法（基于频率分析和随机性）
	redBalls := s.generateRedBalls(&lotteryType, historicalResults)
	blueBalls := s.generateBlueBalls(&lotteryType, historicalResults)
	confidence := s.calculateConfidence(&lotteryType, historicalResults)

	// 创建AI预测记录（使用系统用户ID=1）
	prediction := &models.Prediction{
		UserID:        1, // 系统AI用户
		LotteryTypeID: lotteryTypeID,
		Period:        period,
		RedBalls:      strings.Join(redBalls, ","),
		BlueBalls:     strings.Join(blueBalls, ","),
		Confidence:    confidence,
		Method:        "ai",
		IsPublic:      true,
	}

	if err := s.db.Create(prediction).Error; err != nil {
		return nil, err
	}

	s.db.Preload("LotteryType").First(prediction, prediction.ID)
	return prediction, nil
}

// generateRedBalls 生成红球预测
func (s *LotteryService) generateRedBalls(lotteryType *models.LotteryType, historical []models.DrawResult) []string {
	// 解析红球范围
	rangeParts := strings.Split(lotteryType.RedRange, "-")
	minNum, _ := strconv.Atoi(rangeParts[0])
	maxNum, _ := strconv.Atoi(rangeParts[1])

	// 统计历史频率
	frequency := make(map[int]int)
	for _, result := range historical {
		balls := strings.Split(result.RedBalls, ",")
		for _, ball := range balls {
			if num, err := strconv.Atoi(ball); err == nil {
				frequency[num]++
			}
		}
	}

	// 结合频率和随机性生成预测
	var candidates []int
	for i := minNum; i <= maxNum; i++ {
		// 热号权重更高
		weight := frequency[i] + 1
		for j := 0; j < weight; j++ {
			candidates = append(candidates, i)
		}
	}

	// 随机选择
	rand.Seed(time.Now().UnixNano())
	selected := make(map[int]bool)
	var result []string

	for len(result) < lotteryType.RedBalls {
		idx := rand.Intn(len(candidates))
		num := candidates[idx]
		if !selected[num] {
			selected[num] = true
			result = append(result, fmt.Sprintf("%02d", num))
		}
	}

	sort.Strings(result)
	return result
}

// generateBlueBalls 生成蓝球预测
func (s *LotteryService) generateBlueBalls(lotteryType *models.LotteryType, historical []models.DrawResult) []string {
	if lotteryType.BlueBalls == 0 {
		return []string{}
	}

	rangeParts := strings.Split(lotteryType.BlueRange, "-")
	minNum, _ := strconv.Atoi(rangeParts[0])
	maxNum, _ := strconv.Atoi(rangeParts[1])

	// 统计蓝球频率
	frequency := make(map[int]int)
	for _, result := range historical {
		if result.BlueBalls != "" {
			balls := strings.Split(result.BlueBalls, ",")
			for _, ball := range balls {
				if num, err := strconv.Atoi(ball); err == nil {
					frequency[num]++
				}
			}
		}
	}

	// 生成蓝球预测
	var candidates []int
	for i := minNum; i <= maxNum; i++ {
		weight := frequency[i] + 1
		for j := 0; j < weight; j++ {
			candidates = append(candidates, i)
		}
	}

	rand.Seed(time.Now().UnixNano())
	selected := make(map[int]bool)
	var result []string

	for len(result) < lotteryType.BlueBalls {
		idx := rand.Intn(len(candidates))
		num := candidates[idx]
		if !selected[num] {
			selected[num] = true
			result = append(result, fmt.Sprintf("%02d", num))
		}
	}

	sort.Strings(result)
	return result
}

// calculateConfidence 计算预测置信度
func (s *LotteryService) calculateConfidence(lotteryType *models.LotteryType, historical []models.DrawResult) float64 {
	// 基于历史数据量和模式识别计算置信度
	dataQuality := float64(len(historical)) / 100.0
	if dataQuality > 1.0 {
		dataQuality = 1.0
	}

	// 基础置信度 + 数据质量调整
	baseConfidence := 0.3 + (dataQuality * 0.4)

	// 添加一些随机性
	rand.Seed(time.Now().UnixNano())
	randomFactor := (rand.Float64() - 0.5) * 0.2

	confidence := baseConfidence + randomFactor
	if confidence < 0.1 {
		confidence = 0.1
	}
	if confidence > 0.9 {
		confidence = 0.9
	}

	return confidence
}

// LikePrediction 点赞预测
func (s *LotteryService) LikePrediction(userID, predictionID uint) error {
	// 检查是否已经点赞
	var existing models.PredictionLike
	if err := s.db.Where("user_id = ? AND prediction_id = ?", userID, predictionID).First(&existing).Error; err == nil {
		return fmt.Errorf("already liked this prediction")
	}

	// 创建点赞记录
	like := models.PredictionLike{
		UserID:       userID,
		PredictionID: predictionID,
	}

	if err := s.db.Create(&like).Error; err != nil {
		return err
	}

	// 更新预测点赞数
	return s.db.Model(&models.Prediction{}).Where("id = ?", predictionID).
		UpdateColumn("like_count", gorm.Expr("like_count + ?", 1)).Error
}

// UnlikePrediction 取消点赞
func (s *LotteryService) UnlikePrediction(userID, predictionID uint) error {
	// 删除点赞记录
	if err := s.db.Where("user_id = ? AND prediction_id = ?", userID, predictionID).
		Delete(&models.PredictionLike{}).Error; err != nil {
		return err
	}

	// 更新预测点赞数
	return s.db.Model(&models.Prediction{}).Where("id = ?", predictionID).
		UpdateColumn("like_count", gorm.Expr("like_count - ?", 1)).Error
}

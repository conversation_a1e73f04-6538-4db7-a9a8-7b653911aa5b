package models

import (
	"time"
	"gorm.io/gorm"
)

// LotteryType 彩票类型
type LotteryType struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"not null;uniqueIndex"` // 双色球、大乐透等
	Code        string         `json:"code" gorm:"not null;uniqueIndex"` // ssq, dlt等
	Description string         `json:"description"`
	Rules       string         `json:"rules" gorm:"type:text"`           // 游戏规则JSON
	RedBalls    int            `json:"red_balls"`                        // 红球数量
	BlueBalls   int            `json:"blue_balls"`                       // 蓝球数量
	RedRange    string         `json:"red_range"`                        // 红球范围 "1-33"
	BlueRange   string         `json:"blue_range"`                       // 蓝球范围 "1-16"
	DrawDays    string         `json:"draw_days"`                        // 开奖日期 "2,4,7" (周二、四、日)
	IsActive    bool           `json:"is_active" gorm:"default:true"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	DrawResults []DrawResult `json:"draw_results,omitempty" gorm:"foreignKey:LotteryTypeID"`
	Predictions []Prediction `json:"predictions,omitempty" gorm:"foreignKey:LotteryTypeID"`
}

// DrawResult 开奖结果
type DrawResult struct {
	ID             uint           `json:"id" gorm:"primaryKey"`
	LotteryTypeID  uint           `json:"lottery_type_id" gorm:"not null"`
	Period         string         `json:"period" gorm:"not null"`           // 期号
	DrawDate       time.Time      `json:"draw_date" gorm:"not null"`        // 开奖日期
	RedBalls       string         `json:"red_balls" gorm:"not null"`        // 红球号码 "01,05,12,22,25,33"
	BlueBalls      string         `json:"blue_balls"`                       // 蓝球号码 "07,12"
	SalesAmount    int64          `json:"sales_amount"`                     // 销售额(分)
	PoolAmount     int64          `json:"pool_amount"`                      // 奖池金额(分)
	PrizeDetails   string         `json:"prize_details" gorm:"type:text"`   // 中奖详情JSON
	IsVerified     bool           `json:"is_verified" gorm:"default:false"` // 是否已验证
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	LotteryType LotteryType `json:"lottery_type" gorm:"foreignKey:LotteryTypeID"`
}

// Prediction 用户预测
type Prediction struct {
	ID            uint           `json:"id" gorm:"primaryKey"`
	UserID        uint           `json:"user_id" gorm:"not null"`
	LotteryTypeID uint           `json:"lottery_type_id" gorm:"not null"`
	Period        string         `json:"period" gorm:"not null"`           // 预测期号
	RedBalls      string         `json:"red_balls" gorm:"not null"`        // 预测红球
	BlueBalls     string         `json:"blue_balls"`                       // 预测蓝球
	Confidence    float64        `json:"confidence"`                       // AI置信度 0-1
	Method        string         `json:"method"`                           // 预测方法 "ai", "manual", "follow"
	SourceUserID  *uint          `json:"source_user_id"`                   // 跟单来源用户ID
	IsPublic      bool           `json:"is_public" gorm:"default:true"`    // 是否公开
	LikeCount     int            `json:"like_count" gorm:"default:0"`      // 点赞数
	FollowCount   int            `json:"follow_count" gorm:"default:0"`    // 跟单数
	HitCount      int            `json:"hit_count" gorm:"default:0"`       // 命中号码数
	IsWin         *bool          `json:"is_win"`                           // 是否中奖(null表示未开奖)
	WinAmount     int64          `json:"win_amount"`                       // 中奖金额(分)
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User        User        `json:"user" gorm:"foreignKey:UserID"`
	LotteryType LotteryType `json:"lottery_type" gorm:"foreignKey:LotteryTypeID"`
	SourceUser  *User       `json:"source_user,omitempty" gorm:"foreignKey:SourceUserID"`
	Likes       []PredictionLike `json:"likes,omitempty" gorm:"foreignKey:PredictionID"`
	Follows     []PredictionFollow `json:"follows,omitempty" gorm:"foreignKey:PredictionID"`
}

// PredictionLike 预测点赞
type PredictionLike struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	UserID       uint      `json:"user_id" gorm:"not null"`
	PredictionID uint      `json:"prediction_id" gorm:"not null"`
	CreatedAt    time.Time `json:"created_at"`

	// 关联关系
	User       User       `json:"user" gorm:"foreignKey:UserID"`
	Prediction Prediction `json:"prediction" gorm:"foreignKey:PredictionID"`

	// 复合唯一索引
	// gorm:"uniqueIndex:idx_user_prediction"
}

// PredictionFollow 预测跟单
type PredictionFollow struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	UserID       uint      `json:"user_id" gorm:"not null"`       // 跟单用户
	PredictionID uint      `json:"prediction_id" gorm:"not null"` // 被跟单的预测
	Amount       int64     `json:"amount"`                        // 跟单金额(分)
	CreatedAt    time.Time `json:"created_at"`

	// 关联关系
	User       User       `json:"user" gorm:"foreignKey:UserID"`
	Prediction Prediction `json:"prediction" gorm:"foreignKey:PredictionID"`
}

// UserLotteryStats 用户彩票统计
type UserLotteryStats struct {
	ID               uint           `json:"id" gorm:"primaryKey"`
	UserID           uint           `json:"user_id" gorm:"not null;uniqueIndex"`
	TotalPredictions int            `json:"total_predictions" gorm:"default:0"`     // 总预测次数
	WinPredictions   int            `json:"win_predictions" gorm:"default:0"`       // 中奖次数
	WinRate          float64        `json:"win_rate" gorm:"default:0"`              // 中奖率
	TotalWinAmount   int64          `json:"total_win_amount" gorm:"default:0"`      // 总中奖金额(分)
	FollowerCount    int            `json:"follower_count" gorm:"default:0"`        // 跟单粉丝数
	LikeCount        int            `json:"like_count" gorm:"default:0"`            // 总点赞数
	Rank             int            `json:"rank"`                                   // 排名
	Score            float64        `json:"score" gorm:"default:0"`                 // 综合评分
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// LotteryAnalysis 彩票分析数据
type LotteryAnalysis struct {
	ID            uint           `json:"id" gorm:"primaryKey"`
	LotteryTypeID uint           `json:"lottery_type_id" gorm:"not null"`
	AnalysisType  string         `json:"analysis_type" gorm:"not null"` // "hot_cold", "trend", "pattern"
	Period        string         `json:"period"`                        // 分析期号范围
	Data          string         `json:"data" gorm:"type:text"`         // 分析结果JSON
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	LotteryType LotteryType `json:"lottery_type" gorm:"foreignKey:LotteryTypeID"`
}

// AIModel AI模型信息
type AIModel struct {
	ID            uint           `json:"id" gorm:"primaryKey"`
	LotteryTypeID uint           `json:"lottery_type_id" gorm:"not null"`
	Name          string         `json:"name" gorm:"not null"`
	Version       string         `json:"version" gorm:"not null"`
	Algorithm     string         `json:"algorithm"`                      // 算法类型
	Parameters    string         `json:"parameters" gorm:"type:text"`    // 模型参数JSON
	TrainData     string         `json:"train_data"`                     // 训练数据范围
	Accuracy      float64        `json:"accuracy"`                       // 准确率
	IsActive      bool           `json:"is_active" gorm:"default:false"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	LotteryType LotteryType `json:"lottery_type" gorm:"foreignKey:LotteryTypeID"`
}

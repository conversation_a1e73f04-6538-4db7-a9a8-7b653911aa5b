package models

import (
	"time"
	"gorm.io/gorm"
)

type User struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Username    string         `json:"username" gorm:"uniqueIndex;not null"`
	Email       string         `json:"email" gorm:"uniqueIndex;not null"`
	Password    string         `json:"-" gorm:"not null"`
	DisplayName string         `json:"display_name"`
	Bio         string         `json:"bio"`
	Avatar      string         `json:"avatar"`
	IsVerified  bool           `json:"is_verified" gorm:"default:false"`
	IsActive    bool           `json:"is_active" gorm:"default:true"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Posts     []Post     `json:"posts,omitempty" gorm:"foreignKey:UserID"`
	Likes     []Like     `json:"likes,omitempty" gorm:"foreignKey:UserID"`
	Comments  []Comment  `json:"comments,omitempty" gorm:"foreignKey:UserID"`
	Followers []Follow   `json:"followers,omitempty" gorm:"foreignKey:FollowingID"`
	Following []Follow   `json:"following,omitempty" gorm:"foreignKey:FollowerID"`
}

type Post struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	UserID    uint           `json:"user_id" gorm:"not null"`
	Content   string         `json:"content"`
	Images    []string       `json:"images" gorm:"type:text[]"`
	LikeCount int            `json:"like_count" gorm:"default:0"`
	IsPublic  bool           `json:"is_public" gorm:"default:true"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User     User      `json:"user" gorm:"foreignKey:UserID"`
	Likes    []Like    `json:"likes,omitempty" gorm:"foreignKey:PostID"`
	Comments []Comment `json:"comments,omitempty" gorm:"foreignKey:PostID"`
}

type Like struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"user_id" gorm:"not null"`
	PostID    uint      `json:"post_id" gorm:"not null"`
	CreatedAt time.Time `json:"created_at"`

	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID"`
	Post Post `json:"post" gorm:"foreignKey:PostID"`
}

type Comment struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	UserID    uint           `json:"user_id" gorm:"not null"`
	PostID    uint           `json:"post_id" gorm:"not null"`
	Content   string         `json:"content" gorm:"not null"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID"`
	Post Post `json:"post" gorm:"foreignKey:PostID"`
}

type Follow struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	FollowerID  uint      `json:"follower_id" gorm:"not null"`
	FollowingID uint      `json:"following_id" gorm:"not null"`
	CreatedAt   time.Time `json:"created_at"`

	// 关联关系
	Follower  User `json:"follower" gorm:"foreignKey:FollowerID"`
	Following User `json:"following" gorm:"foreignKey:FollowingID"`
}

type Notification struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	UserID    uint           `json:"user_id" gorm:"not null"`
	Type      string         `json:"type" gorm:"not null"` // like, comment, follow, etc.
	Title     string         `json:"title"`
	Message   string         `json:"message"`
	Data      string         `json:"data"` // JSON data for additional info
	IsRead    bool           `json:"is_read" gorm:"default:false"`
	CreatedAt time.Time      `json:"created_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID"`
}

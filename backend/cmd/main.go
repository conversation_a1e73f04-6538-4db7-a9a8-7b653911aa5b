package main

import (
	"log"
	"social-backend/internal/api"
	"social-backend/internal/config"
	"social-backend/internal/db"

	"github.com/joho/godotenv"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// 初始化数据库
	database, err := db.Initialize(cfg.DatabaseURL)
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// 获取底层的sql.DB实例用于关闭连接
	sqlDB, err := database.DB()
	if err != nil {
		log.Fatal("Failed to get database instance:", err)
	}
	defer sqlDB.Close()

	// 运行数据库迁移
	if err := db.<PERSON>(database); err != nil {
		log.Fatal("Failed to run migrations:", err)
	}

	// 初始化API服务器
	server := api.NewServer(cfg, database)

	// 启动服务器
	log.Printf("Server starting on port %s", cfg.Port)
	if err := server.Start(); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}

import 'package:social_app/core/services/api_service.dart';

void main() async {
  // 测试API连接
  final dio = DioConfig.createDio();
  final apiService = ApiService(dio);
  
  try {
    print('测试API连接...');
    
    // 测试健康检查（这个端点不需要认证）
    final response = await dio.get('http://localhost:8080/api/v1/health');
    print('健康检查响应: ${response.data}');
    
    // 测试用户注册
    print('\n测试用户注册...');
    final registerRequest = RegisterRequest(
      username: 'fluttertest',
      email: '<EMAIL>',
      password: 'password123',
      displayName: 'Flutter Test User',
    );
    
    final authResponse = await apiService.register(registerRequest);
    print('注册成功!');
    print('Token: ${authResponse.token}');
    print('用户: ${authResponse.user.displayName}');
    
    // 测试获取当前用户信息
    print('\n测试获取用户信息...');
    final userProfile = await apiService.getCurrentUser();
    print('用户资料: ${userProfile.user.displayName}');
    print('关注者数量: ${userProfile.followerCount}');
    
  } catch (e) {
    print('API测试失败: $e');
  }
}

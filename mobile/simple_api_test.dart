import 'package:dio/dio.dart';

void main() async {
  // 简单的API测试，不依赖Flutter
  final dio = Dio();
  
  try {
    print('测试API连接...');
    
    // 测试健康检查
    final response = await dio.get('http://localhost:8080/api/v1/health');
    print('健康检查响应: ${response.data}');
    
    // 测试用户注册
    print('\n测试用户注册...');
    final registerData = {
      'username': 'fluttertest2',
      'email': '<EMAIL>',
      'password': 'password123',
      'display_name': 'Flutter Test User 2',
    };
    
    final registerResponse = await dio.post(
      'http://localhost:8080/api/v1/auth/register',
      data: registerData,
    );
    
    print('注册成功!');
    print('响应: ${registerResponse.data}');
    
    // 提取token
    final token = registerResponse.data['token'];
    print('Token: $token');
    
    // 测试获取当前用户信息
    print('\n测试获取用户信息...');
    final userResponse = await dio.get(
      'http://localhost:8080/api/v1/users/me',
      options: Options(
        headers: {'Authorization': 'Bearer $token'},
      ),
    );
    
    print('用户信息响应: ${userResponse.data}');
    
  } catch (e) {
    if (e is DioException) {
      print('API测试失败: ${e.message}');
      print('响应数据: ${e.response?.data}');
    } else {
      print('API测试失败: $e');
    }
  }
}

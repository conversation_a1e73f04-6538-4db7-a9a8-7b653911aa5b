// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lottery_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LotteryType _$LotteryTypeFromJson(Map<String, dynamic> json) => LotteryType(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      code: json['code'] as String,
      description: json['description'] as String,
      rules: json['rules'] as String,
      redBalls: (json['red_balls'] as num).toInt(),
      blueBalls: (json['blue_balls'] as num).toInt(),
      redRange: json['red_range'] as String,
      blueRange: json['blue_range'] as String,
      drawDays: json['draw_days'] as String,
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$LotteryTypeToJson(LotteryType instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'code': instance.code,
      'description': instance.description,
      'rules': instance.rules,
      'red_balls': instance.redBalls,
      'blue_balls': instance.blueBalls,
      'red_range': instance.redRange,
      'blue_range': instance.blueRange,
      'draw_days': instance.drawDays,
      'is_active': instance.isActive,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

LotteryUser _$LotteryUserFromJson(Map<String, dynamic> json) => LotteryUser(
      id: (json['id'] as num).toInt(),
      username: json['username'] as String,
      email: json['email'] as String,
      displayName: json['display_name'] as String,
      bio: json['bio'] as String,
      avatar: json['avatar'] as String,
      isVerified: json['is_verified'] as bool,
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$LotteryUserToJson(LotteryUser instance) =>
    <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'email': instance.email,
      'display_name': instance.displayName,
      'bio': instance.bio,
      'avatar': instance.avatar,
      'is_verified': instance.isVerified,
      'is_active': instance.isActive,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

Prediction _$PredictionFromJson(Map<String, dynamic> json) => Prediction(
      id: (json['id'] as num).toInt(),
      userId: (json['user_id'] as num).toInt(),
      lotteryTypeId: (json['lottery_type_id'] as num).toInt(),
      period: json['period'] as String,
      redBalls: json['red_balls'] as String,
      blueBalls: json['blue_balls'] as String,
      confidence: (json['confidence'] as num).toDouble(),
      method: json['method'] as String,
      sourceUserId: (json['source_user_id'] as num?)?.toInt(),
      isPublic: json['is_public'] as bool,
      likeCount: (json['like_count'] as num).toInt(),
      followCount: (json['follow_count'] as num).toInt(),
      hitCount: (json['hit_count'] as num).toInt(),
      isWin: json['is_win'] as bool?,
      winAmount: (json['win_amount'] as num).toInt(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      user: json['user'] == null
          ? null
          : LotteryUser.fromJson(json['user'] as Map<String, dynamic>),
      lotteryType: json['lottery_type'] == null
          ? null
          : LotteryType.fromJson(json['lottery_type'] as Map<String, dynamic>),
      sourceUser: json['source_user'] == null
          ? null
          : LotteryUser.fromJson(json['source_user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PredictionToJson(Prediction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'lottery_type_id': instance.lotteryTypeId,
      'period': instance.period,
      'red_balls': instance.redBalls,
      'blue_balls': instance.blueBalls,
      'confidence': instance.confidence,
      'method': instance.method,
      'source_user_id': instance.sourceUserId,
      'is_public': instance.isPublic,
      'like_count': instance.likeCount,
      'follow_count': instance.followCount,
      'hit_count': instance.hitCount,
      'is_win': instance.isWin,
      'win_amount': instance.winAmount,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'user': instance.user,
      'lottery_type': instance.lotteryType,
      'source_user': instance.sourceUser,
    };

LotteryTypesResponse _$LotteryTypesResponseFromJson(
        Map<String, dynamic> json) =>
    LotteryTypesResponse(
      lotteryTypes: (json['lottery_types'] as List<dynamic>)
          .map((e) => LotteryType.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$LotteryTypesResponseToJson(
        LotteryTypesResponse instance) =>
    <String, dynamic>{
      'lottery_types': instance.lotteryTypes,
    };

PredictionsResponse _$PredictionsResponseFromJson(Map<String, dynamic> json) =>
    PredictionsResponse(
      predictions: (json['predictions'] as List<dynamic>)
          .map((e) => Prediction.fromJson(e as Map<String, dynamic>))
          .toList(),
      page: (json['page'] as num).toInt(),
      limit: (json['limit'] as num).toInt(),
    );

Map<String, dynamic> _$PredictionsResponseToJson(
        PredictionsResponse instance) =>
    <String, dynamic>{
      'predictions': instance.predictions,
      'page': instance.page,
      'limit': instance.limit,
    };

PredictionResponse _$PredictionResponseFromJson(Map<String, dynamic> json) =>
    PredictionResponse(
      message: json['message'] as String,
      prediction:
          Prediction.fromJson(json['prediction'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PredictionResponseToJson(PredictionResponse instance) =>
    <String, dynamic>{
      'message': instance.message,
      'prediction': instance.prediction,
    };

AIPredictionRequest _$AIPredictionRequestFromJson(Map<String, dynamic> json) =>
    AIPredictionRequest(
      lotteryTypeId: (json['lottery_type_id'] as num).toInt(),
      period: json['period'] as String,
    );

Map<String, dynamic> _$AIPredictionRequestToJson(
        AIPredictionRequest instance) =>
    <String, dynamic>{
      'lottery_type_id': instance.lotteryTypeId,
      'period': instance.period,
    };

import 'package:json_annotation/json_annotation.dart';

part 'lottery_models.g.dart';

@JsonSerializable()
class LotteryType {
  final int id;
  final String name;
  final String code;
  final String description;
  final String rules;
  @Json<PERSON><PERSON>(name: 'red_balls')
  final int redBalls;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'blue_balls')
  final int blueBalls;
  @Json<PERSON>ey(name: 'red_range')
  final String redRange;
  @Json<PERSON>ey(name: 'blue_range')
  final String blueRange;
  @<PERSON>son<PERSON>ey(name: 'draw_days')
  final String drawDays;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_active')
  final bool isActive;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  @Json<PERSON><PERSON>(name: 'updated_at')
  final DateTime updatedAt;

  LotteryType({
    required this.id,
    required this.name,
    required this.code,
    required this.description,
    required this.rules,
    required this.redBalls,
    required this.blueBalls,
    required this.redRange,
    required this.blueRange,
    required this.drawDays,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory LotteryType.fromJson(Map<String, dynamic> json) =>
      _$LotteryTypeFromJson(json);

  Map<String, dynamic> toJson() => _$LotteryTypeToJson(this);
}

@JsonSerializable()
class LotteryUser {
  final int id;
  final String username;
  final String email;
  @JsonKey(name: 'display_name')
  final String displayName;
  final String bio;
  final String avatar;
  @JsonKey(name: 'is_verified')
  final bool isVerified;
  @JsonKey(name: 'is_active')
  final bool isActive;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  LotteryUser({
    required this.id,
    required this.username,
    required this.email,
    required this.displayName,
    required this.bio,
    required this.avatar,
    required this.isVerified,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory LotteryUser.fromJson(Map<String, dynamic> json) =>
      _$LotteryUserFromJson(json);

  Map<String, dynamic> toJson() => _$LotteryUserToJson(this);
}

@JsonSerializable()
class Prediction {
  final int id;
  @JsonKey(name: 'user_id')
  final int userId;
  @JsonKey(name: 'lottery_type_id')
  final int lotteryTypeId;
  final String period;
  @JsonKey(name: 'red_balls')
  final String redBalls;
  @JsonKey(name: 'blue_balls')
  final String blueBalls;
  final double confidence;
  final String method;
  @JsonKey(name: 'source_user_id')
  final int? sourceUserId;
  @JsonKey(name: 'is_public')
  final bool isPublic;
  @JsonKey(name: 'like_count')
  final int likeCount;
  @JsonKey(name: 'follow_count')
  final int followCount;
  @JsonKey(name: 'hit_count')
  final int hitCount;
  @JsonKey(name: 'is_win')
  final bool? isWin;
  @JsonKey(name: 'win_amount')
  final int winAmount;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
  final LotteryUser? user;
  @JsonKey(name: 'lottery_type')
  final LotteryType? lotteryType;
  @JsonKey(name: 'source_user')
  final LotteryUser? sourceUser;

  Prediction({
    required this.id,
    required this.userId,
    required this.lotteryTypeId,
    required this.period,
    required this.redBalls,
    required this.blueBalls,
    required this.confidence,
    required this.method,
    this.sourceUserId,
    required this.isPublic,
    required this.likeCount,
    required this.followCount,
    required this.hitCount,
    this.isWin,
    required this.winAmount,
    required this.createdAt,
    required this.updatedAt,
    this.user,
    this.lotteryType,
    this.sourceUser,
  });

  factory Prediction.fromJson(Map<String, dynamic> json) =>
      _$PredictionFromJson(json);

  Map<String, dynamic> toJson() => _$PredictionToJson(this);

  // 解析红球号码
  List<String> get redBallsList {
    return redBalls.split(',').map((e) => e.trim()).toList();
  }

  // 解析蓝球号码
  List<String> get blueBallsList {
    if (blueBalls.isEmpty) return [];
    return blueBalls.split(',').map((e) => e.trim()).toList();
  }
}

@JsonSerializable()
class LotteryTypesResponse {
  @JsonKey(name: 'lottery_types')
  final List<LotteryType> lotteryTypes;

  LotteryTypesResponse({required this.lotteryTypes});

  factory LotteryTypesResponse.fromJson(Map<String, dynamic> json) =>
      _$LotteryTypesResponseFromJson(json);

  Map<String, dynamic> toJson() => _$LotteryTypesResponseToJson(this);
}

@JsonSerializable()
class PredictionsResponse {
  final List<Prediction> predictions;
  final int page;
  final int limit;

  PredictionsResponse({
    required this.predictions,
    required this.page,
    required this.limit,
  });

  factory PredictionsResponse.fromJson(Map<String, dynamic> json) =>
      _$PredictionsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$PredictionsResponseToJson(this);
}

@JsonSerializable()
class PredictionResponse {
  final String message;
  final Prediction prediction;

  PredictionResponse({
    required this.message,
    required this.prediction,
  });

  factory PredictionResponse.fromJson(Map<String, dynamic> json) =>
      _$PredictionResponseFromJson(json);

  Map<String, dynamic> toJson() => _$PredictionResponseToJson(this);
}

@JsonSerializable()
class AIPredictionRequest {
  @JsonKey(name: 'lottery_type_id')
  final int lotteryTypeId;
  final String period;

  AIPredictionRequest({
    required this.lotteryTypeId,
    required this.period,
  });

  factory AIPredictionRequest.fromJson(Map<String, dynamic> json) =>
      _$AIPredictionRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AIPredictionRequestToJson(this);
}

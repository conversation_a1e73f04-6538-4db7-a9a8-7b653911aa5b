import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

@JsonSerializable()
class User {
  final int id;
  final String username;
  final String email;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'display_name')
  final String displayName;
  final String? bio;
  final String? avatar;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_verified')
  final bool isVerified;
  @Json<PERSON>ey(name: 'is_active')
  final bool isActive;
  @<PERSON>son<PERSON>ey(name: 'created_at')
  final DateTime createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final DateTime updatedAt;

  const User({
    required this.id,
    required this.username,
    required this.email,
    required this.displayName,
    this.bio,
    this.avatar,
    this.isVerified = false,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  User copyWith({
    int? id,
    String? username,
    String? email,
    String? displayName,
    String? bio,
    String? avatar,
    bool? isVerified,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      bio: bio ?? this.bio,
      avatar: avatar ?? this.avatar,
      isVerified: isVerified ?? this.isVerified,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class UserProfile {
  final User user;
  @JsonKey(name: 'follower_count')
  final int followerCount;
  @JsonKey(name: 'following_count')
  final int followingCount;
  @JsonKey(name: 'post_count')
  final int postCount;
  @JsonKey(name: 'is_following')
  final bool? isFollowing;

  const UserProfile({
    required this.user,
    required this.followerCount,
    required this.followingCount,
    required this.postCount,
    this.isFollowing,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) => _$UserProfileFromJson(json);
  Map<String, dynamic> toJson() => _$UserProfileToJson(this);
}

import 'package:json_annotation/json_annotation.dart';
import 'user.dart';

part 'post.g.dart';

@JsonSerializable()
class Post {
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id')
  final int userId;
  final String content;
  final List<String> images;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'like_count')
  final int likeCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_public')
  final bool isPublic;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final DateTime updatedAt;
  final User user;
  final List<Comment>? comments;
  @Json<PERSON>ey(name: 'is_liked')
  final bool? isLiked;

  const Post({
    required this.id,
    required this.userId,
    required this.content,
    this.images = const [],
    required this.likeCount,
    this.isPublic = true,
    required this.createdAt,
    required this.updatedAt,
    required this.user,
    this.comments,
    this.isLiked,
  });

  factory Post.fromJson(Map<String, dynamic> json) => _$Post<PERSON>romJson(json);
  Map<String, dynamic> toJson() => _$PostToJson(this);

  Post copyWith({
    int? id,
    int? userId,
    String? content,
    List<String>? images,
    int? likeCount,
    bool? isPublic,
    DateTime? createdAt,
    DateTime? updatedAt,
    User? user,
    List<Comment>? comments,
    bool? isLiked,
  }) {
    return Post(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      content: content ?? this.content,
      images: images ?? this.images,
      likeCount: likeCount ?? this.likeCount,
      isPublic: isPublic ?? this.isPublic,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      user: user ?? this.user,
      comments: comments ?? this.comments,
      isLiked: isLiked ?? this.isLiked,
    );
  }
}

@JsonSerializable()
class Comment {
  final int id;
  @JsonKey(name: 'user_id')
  final int userId;
  @JsonKey(name: 'post_id')
  final int postId;
  final String content;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
  final User user;

  const Comment({
    required this.id,
    required this.userId,
    required this.postId,
    required this.content,
    required this.createdAt,
    required this.updatedAt,
    required this.user,
  });

  factory Comment.fromJson(Map<String, dynamic> json) => _$CommentFromJson(json);
  Map<String, dynamic> toJson() => _$CommentToJson(this);
}

@JsonSerializable()
class CreatePostRequest {
  final String content;
  final List<String> images;
  @JsonKey(name: 'is_public')
  final bool isPublic;

  const CreatePostRequest({
    required this.content,
    this.images = const [],
    this.isPublic = true,
  });

  factory CreatePostRequest.fromJson(Map<String, dynamic> json) => _$CreatePostRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CreatePostRequestToJson(this);
}

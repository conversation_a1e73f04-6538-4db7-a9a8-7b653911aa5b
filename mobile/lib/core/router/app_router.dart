import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:social_app/core/services/storage_service.dart';
import 'package:social_app/screens/auth/login_screen.dart';
import 'package:social_app/screens/auth/register_screen.dart';
import 'package:social_app/screens/home/<USER>';
import 'package:social_app/screens/profile/profile_screen.dart';
import 'package:social_app/screens/post/create_post_screen.dart';
import 'package:social_app/screens/post/post_detail_screen.dart';
import 'package:social_app/screens/splash/splash_screen.dart';

final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/splash',
    redirect: (context, state) {
      final isLoggedIn = StorageService.isLoggedIn();
      final isOnSplash = state.uri.toString() == '/splash';
      final isOnAuth = state.uri.toString() == '/login' || state.uri.toString() == '/register';
      
      // 如果在启动页，不重定向
      if (isOnSplash) return null;
      
      // 如果未登录且不在认证页面，重定向到登录页
      if (!isLoggedIn && !isOnAuth) return '/login';
      
      // 如果已登录且在认证页面，重定向到首页
      if (isLoggedIn && isOnAuth) return '/home';
      
      return null;
    },
    routes: [
      GoRoute(
        path: '/splash',
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: '/login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/register',
        builder: (context, state) => const RegisterScreen(),
      ),
      GoRoute(
        path: '/home',
        builder: (context, state) => const HomeScreen(),
      ),
      GoRoute(
        path: '/profile/:userId',
        builder: (context, state) {
          final userId = int.parse(state.pathParameters['userId']!);
          return ProfileScreen(userId: userId);
        },
      ),
      GoRoute(
        path: '/create-post',
        builder: (context, state) => const CreatePostScreen(),
      ),
      GoRoute(
        path: '/post/:postId',
        builder: (context, state) {
          final postId = int.parse(state.pathParameters['postId']!);
          return PostDetailScreen(postId: postId);
        },
      ),
    ],
  );
});

// 路由扩展方法
extension AppRouterExtension on GoRouter {
  void pushLogin() => push('/login');
  void pushRegister() => push('/register');
  void pushHome() => go('/home');
  void pushProfile(int userId) => push('/profile/$userId');
  void pushCreatePost() => push('/create-post');
  void pushPostDetail(int postId) => push('/post/$postId');
}

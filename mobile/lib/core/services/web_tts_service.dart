import 'dart:developer' as developer;
import 'dart:html' as html;
import 'dart:js' as js;

/// Web版本的TTS服务，使用浏览器原生Speech API
class WebTTSService {
  static final WebTTSService _instance = WebTTSService._internal();
  factory WebTTSService() => _instance;
  WebTTSService._internal();

  bool _isInitialized = false;
  bool _isSpeaking = false;
  bool _isSupported = false;

  /// 初始化Web TTS服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 检查浏览器是否支持Speech Synthesis API
      _isSupported = js.context.hasProperty('speechSynthesis');
      
      if (_isSupported) {
        developer.log('Web Speech API 支持检测成功', name: 'WebTTSService');
        _isInitialized = true;
      } else {
        developer.log('浏览器不支持 Web Speech API', name: 'WebTTSService');
      }
    } catch (e) {
      developer.log('Web TTS服务初始化失败: $e', name: 'WebTTSService');
    }
  }

  /// 播报文字
  Future<void> speak(String text) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (!_isSupported || text.isEmpty) {
      developer.log('TTS不支持或文本为空', name: 'WebTTSService');
      return;
    }

    try {
      // 停止当前播报
      await stop();

      // 使用JavaScript调用Web Speech API
      js.context.callMethod('eval', ['''
        (function() {
          if ('speechSynthesis' in window) {
            var utterance = new SpeechSynthesisUtterance('$text');
            utterance.lang = 'zh-CN';
            utterance.rate = 0.8;
            utterance.pitch = 1.0;
            utterance.volume = 1.0;
            
            utterance.onstart = function() {
              console.log('TTS开始播报: $text');
            };
            
            utterance.onend = function() {
              console.log('TTS播报完成');
            };
            
            utterance.onerror = function(event) {
              console.error('TTS播报错误:', event.error);
            };
            
            window.speechSynthesis.speak(utterance);
            return true;
          }
          return false;
        })()
      ''']);

      _isSpeaking = true;
      developer.log('开始播报: $text', name: 'WebTTSService');
    } catch (e) {
      developer.log('播报失败: $e', name: 'WebTTSService');
    }
  }

  /// 停止播报
  Future<void> stop() async {
    if (!_isSupported) return;

    try {
      js.context.callMethod('eval', ['''
        if ('speechSynthesis' in window) {
          window.speechSynthesis.cancel();
        }
      ''']);
      _isSpeaking = false;
      developer.log('停止播报', name: 'WebTTSService');
    } catch (e) {
      developer.log('停止播报失败: $e', name: 'WebTTSService');
    }
  }

  /// 暂停播报
  Future<void> pause() async {
    if (!_isSupported) return;

    try {
      js.context.callMethod('eval', ['''
        if ('speechSynthesis' in window) {
          window.speechSynthesis.pause();
        }
      ''']);
      developer.log('暂停播报', name: 'WebTTSService');
    } catch (e) {
      developer.log('暂停播报失败: $e', name: 'WebTTSService');
    }
  }

  /// 恢复播报
  Future<void> resume() async {
    if (!_isSupported) return;

    try {
      js.context.callMethod('eval', ['''
        if ('speechSynthesis' in window) {
          window.speechSynthesis.resume();
        }
      ''']);
      developer.log('恢复播报', name: 'WebTTSService');
    } catch (e) {
      developer.log('恢复播报失败: $e', name: 'WebTTSService');
    }
  }

  /// 设置语速
  Future<void> setSpeechRate(double rate) async {
    // Web Speech API的语速在创建utterance时设置
    developer.log('语速将在下次播报时生效: $rate', name: 'WebTTSService');
  }

  /// 设置音量
  Future<void> setVolume(double volume) async {
    // Web Speech API的音量在创建utterance时设置
    developer.log('音量将在下次播报时生效: $volume', name: 'WebTTSService');
  }

  /// 设置音调
  Future<void> setPitch(double pitch) async {
    // Web Speech API的音调在创建utterance时设置
    developer.log('音调将在下次播报时生效: $pitch', name: 'WebTTSService');
  }

  /// 获取可用语音列表
  Future<List<String>> getVoices() async {
    if (!_isSupported) return [];

    try {
      final result = js.context.callMethod('eval', ['''
        (function() {
          if ('speechSynthesis' in window) {
            var voices = window.speechSynthesis.getVoices();
            return voices.map(function(voice) {
              return voice.name + ' (' + voice.lang + ')';
            });
          }
          return [];
        })()
      ''']);
      
      if (result != null) {
        return List<String>.from(result);
      }
    } catch (e) {
      developer.log('获取语音列表失败: $e', name: 'WebTTSService');
    }
    return [];
  }

  /// 检查是否正在播报
  bool get isSpeaking => _isSpeaking;

  /// 检查是否已初始化
  bool get isInitialized => _isInitialized;

  /// 检查是否支持TTS
  bool get isSupported => _isSupported;

  /// 释放资源
  Future<void> dispose() async {
    if (!_isInitialized) return;

    try {
      await stop();
      _isInitialized = false;
      developer.log('Web TTS服务已释放', name: 'WebTTSService');
    } catch (e) {
      developer.log('Web TTS服务释放失败: $e', name: 'WebTTSService');
    }
  }
}

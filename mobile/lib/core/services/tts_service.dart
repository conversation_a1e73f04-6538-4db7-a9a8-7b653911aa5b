import 'dart:developer' as developer;
import 'package:flutter_tts/flutter_tts.dart';

/// TTS (Text-to-Speech) 语音播报服务
class TTSService {
  static final TTSService _instance = TTSService._internal();
  factory TTSService() => _instance;
  TTSService._internal();

  late FlutterTts _flutterTts;
  bool _isInitialized = false;
  bool _isSpeaking = false;

  /// 初始化TTS服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _flutterTts = FlutterTts();
      
      // 设置语言为中文
      await _flutterTts.setLanguage("zh-CN");
      
      // 设置语音参数
      await _flutterTts.setSpeechRate(0.5); // 语速：0.0-1.0
      await _flutterTts.setVolume(1.0); // 音量：0.0-1.0
      await _flutterTts.setPitch(1.0); // 音调：0.5-2.0
      
      // 设置回调
      _flutterTts.setStartHandler(() {
        _isSpeaking = true;
        developer.log('TTS开始播报', name: 'TTSService');
      });

      _flutterTts.setCompletionHandler(() {
        _isSpeaking = false;
        developer.log('TTS播报完成', name: 'TTSService');
      });

      _flutterTts.setErrorHandler((msg) {
        _isSpeaking = false;
        developer.log('TTS播报错误: $msg', name: 'TTSService');
      });

      _isInitialized = true;
      developer.log('TTS服务初始化成功', name: 'TTSService');
    } catch (e) {
      developer.log('TTS服务初始化失败: $e', name: 'TTSService');
    }
  }

  /// 播报文字
  Future<void> speak(String text) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (text.isEmpty) return;

    try {
      // 停止当前播报
      await stop();
      
      // 开始新的播报
      await _flutterTts.speak(text);
      developer.log('开始播报: $text', name: 'TTSService');
    } catch (e) {
      developer.log('播报失败: $e', name: 'TTSService');
    }
  }

  /// 停止播报
  Future<void> stop() async {
    if (!_isInitialized) return;

    try {
      await _flutterTts.stop();
      _isSpeaking = false;
    } catch (e) {
      developer.log('停止播报失败: $e', name: 'TTSService');
    }
  }

  /// 暂停播报
  Future<void> pause() async {
    if (!_isInitialized) return;

    try {
      await _flutterTts.pause();
    } catch (e) {
      developer.log('暂停播报失败: $e', name: 'TTSService');
    }
  }

  /// 设置语速
  Future<void> setSpeechRate(double rate) async {
    if (!_isInitialized) return;

    try {
      await _flutterTts.setSpeechRate(rate);
    } catch (e) {
      developer.log('设置语速失败: $e', name: 'TTSService');
    }
  }

  /// 设置音量
  Future<void> setVolume(double volume) async {
    if (!_isInitialized) return;

    try {
      await _flutterTts.setVolume(volume);
    } catch (e) {
      developer.log('设置音量失败: $e', name: 'TTSService');
    }
  }

  /// 设置音调
  Future<void> setPitch(double pitch) async {
    if (!_isInitialized) return;

    try {
      await _flutterTts.setPitch(pitch);
    } catch (e) {
      developer.log('设置音调失败: $e', name: 'TTSService');
    }
  }

  /// 获取可用语言列表
  Future<List<String>> getLanguages() async {
    if (!_isInitialized) await initialize();

    try {
      final languages = await _flutterTts.getLanguages;
      return List<String>.from(languages);
    } catch (e) {
      developer.log('获取语言列表失败: $e', name: 'TTSService');
      return [];
    }
  }

  /// 获取可用语音列表
  Future<List<Map<String, String>>> getVoices() async {
    if (!_isInitialized) await initialize();

    try {
      final voices = await _flutterTts.getVoices;
      return List<Map<String, String>>.from(voices);
    } catch (e) {
      developer.log('获取语音列表失败: $e', name: 'TTSService');
      return [];
    }
  }

  /// 检查是否正在播报
  bool get isSpeaking => _isSpeaking;

  /// 检查是否已初始化
  bool get isInitialized => _isInitialized;

  /// 释放资源
  Future<void> dispose() async {
    if (!_isInitialized) return;

    try {
      await stop();
      _isInitialized = false;
      developer.log('TTS服务已释放', name: 'TTSService');
    } catch (e) {
      developer.log('TTS服务释放失败: $e', name: 'TTSService');
    }
  }
}

import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  static late Box _userBox;
  static late SharedPreferences _prefs;
  
  static const String _userBoxName = 'user_data';
  static const String _tokenKey = 'auth_token';
  static const String _userIdKey = 'user_id';
  static const String _usernameKey = 'username';
  
  static Future<void> init() async {
    _userBox = await Hive.openBox(_userBoxName);
    _prefs = await SharedPreferences.getInstance();
  }
  
  // Token管理
  static Future<void> saveToken(String token) async {
    await _prefs.setString(_tokenKey, token);
  }
  
  static String? getToken() {
    return _prefs.getString(_tokenKey);
  }
  
  static Future<void> removeToken() async {
    await _prefs.remove(_tokenKey);
  }
  
  // 用户信息管理
  static Future<void> saveUserInfo({
    required int userId,
    required String username,
  }) async {
    await _prefs.setInt(_userIdKey, userId);
    await _prefs.setString(_usernameKey, username);
  }
  
  static int? getUserId() {
    return _prefs.getInt(_userIdKey);
  }
  
  static String? getUsername() {
    return _prefs.getString(_usernameKey);
  }
  
  static Future<void> clearUserInfo() async {
    await _prefs.remove(_userIdKey);
    await _prefs.remove(_usernameKey);
    await _prefs.remove(_tokenKey);
  }
  
  // 检查是否已登录
  static bool isLoggedIn() {
    return getToken() != null && getUserId() != null;
  }
  
  // 缓存数据管理
  static Future<void> cacheData(String key, dynamic data) async {
    await _userBox.put(key, data);
  }
  
  static T? getCachedData<T>(String key) {
    return _userBox.get(key) as T?;
  }
  
  static Future<void> removeCachedData(String key) async {
    await _userBox.delete(key);
  }
  
  static Future<void> clearCache() async {
    await _userBox.clear();
  }
}

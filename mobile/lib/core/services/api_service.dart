import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:social_app/models/user.dart';
import 'package:social_app/models/post.dart';
import 'package:social_app/core/services/storage_service.dart';

part 'api_service.g.dart';

@RestApi(baseUrl: "http://localhost:8080/api/v1")
abstract class ApiService {
  factory ApiService(Dio dio, {String baseUrl}) = _ApiService;

  // 认证相关
  @POST("/auth/register")
  Future<AuthResponse> register(@Body() RegisterRequest request);

  @POST("/auth/login")
  Future<AuthResponse> login(@Body() LoginRequest request);

  @POST("/auth/refresh")
  Future<TokenResponse> refreshToken();

  // 用户相关
  @GET("/users/me")
  Future<UserProfile> getCurrentUser();

  @PUT("/users/me")
  Future<ApiResponse> updateProfile(@Body() UpdateProfileRequest request);

  @POST("/users/me/avatar")
  @MultiPart()
  Future<ApiResponse> uploadAvatar(@Part() File avatar);

  @GET("/users/{id}")
  Future<UserProfile> getUserProfile(@Path() int id);

  @POST("/users/{id}/follow")
  Future<ApiResponse> followUser(@Path() int id);

  @DELETE("/users/{id}/follow")
  Future<ApiResponse> unfollowUser(@Path() int id);

  // 动态相关
  @GET("/posts")
  Future<PostsResponse> getFeed({
    @Query("page") int page = 1,
    @Query("limit") int limit = 20,
  });

  @POST("/posts")
  Future<PostResponse> createPost(@Body() CreatePostRequest request);

  @GET("/posts/{id}")
  Future<Post> getPost(@Path() int id);

  @PUT("/posts/{id}")
  Future<PostResponse> updatePost(@Path() int id, @Body() CreatePostRequest request);

  @DELETE("/posts/{id}")
  Future<ApiResponse> deletePost(@Path() int id);

  @POST("/posts/{id}/like")
  Future<ApiResponse> likePost(@Path() int id);

  @DELETE("/posts/{id}/like")
  Future<ApiResponse> unlikePost(@Path() int id);

  @GET("/posts/{id}/comments")
  Future<CommentsResponse> getComments(@Path() int id, {
    @Query("page") int page = 1,
    @Query("limit") int limit = 20,
  });

  @POST("/posts/{id}/comments")
  Future<CommentResponse> createComment(@Path() int id, @Body() CreateCommentRequest request);

  // 通知相关
  @GET("/notifications")
  Future<NotificationsResponse> getNotifications({
    @Query("page") int page = 1,
    @Query("limit") int limit = 20,
  });

  @PUT("/notifications/{id}/read")
  Future<ApiResponse> markNotificationAsRead(@Path() int id);
}

// 请求和响应模型
class RegisterRequest {
  final String username;
  final String email;
  final String password;
  final String displayName;

  RegisterRequest({
    required this.username,
    required this.email,
    required this.password,
    required this.displayName,
  });

  Map<String, dynamic> toJson() => {
    'username': username,
    'email': email,
    'password': password,
    'display_name': displayName,
  };
}

class LoginRequest {
  final String email;
  final String password;

  LoginRequest({required this.email, required this.password});

  Map<String, dynamic> toJson() => {
    'email': email,
    'password': password,
  };
}

class UpdateProfileRequest {
  final String displayName;
  final String bio;

  UpdateProfileRequest({required this.displayName, required this.bio});

  Map<String, dynamic> toJson() => {
    'display_name': displayName,
    'bio': bio,
  };
}

class CreateCommentRequest {
  final String content;

  CreateCommentRequest({required this.content});

  Map<String, dynamic> toJson() => {'content': content};
}

class AuthResponse {
  final String token;
  final User user;

  AuthResponse({required this.token, required this.user});

  factory AuthResponse.fromJson(Map<String, dynamic> json) => AuthResponse(
    token: json['token'],
    user: User.fromJson(json['user']),
  );
}

class TokenResponse {
  final String token;

  TokenResponse({required this.token});

  factory TokenResponse.fromJson(Map<String, dynamic> json) => TokenResponse(
    token: json['token'],
  );
}

class ApiResponse {
  final String message;

  ApiResponse({required this.message});

  factory ApiResponse.fromJson(Map<String, dynamic> json) => ApiResponse(
    message: json['message'],
  );
}

class PostsResponse {
  final List<Post> posts;
  final int page;
  final int limit;

  PostsResponse({required this.posts, required this.page, required this.limit});

  factory PostsResponse.fromJson(Map<String, dynamic> json) => PostsResponse(
    posts: (json['posts'] as List).map((e) => Post.fromJson(e)).toList(),
    page: json['page'],
    limit: json['limit'],
  );
}

class PostResponse {
  final String message;
  final Post post;

  PostResponse({required this.message, required this.post});

  factory PostResponse.fromJson(Map<String, dynamic> json) => PostResponse(
    message: json['message'],
    post: Post.fromJson(json['post']),
  );
}

class CommentsResponse {
  final List<Comment> comments;
  final int page;
  final int limit;

  CommentsResponse({required this.comments, required this.page, required this.limit});

  factory CommentsResponse.fromJson(Map<String, dynamic> json) => CommentsResponse(
    comments: (json['comments'] as List).map((e) => Comment.fromJson(e)).toList(),
    page: json['page'],
    limit: json['limit'],
  );
}

class CommentResponse {
  final String message;
  final Comment comment;

  CommentResponse({required this.message, required this.comment});

  factory CommentResponse.fromJson(Map<String, dynamic> json) => CommentResponse(
    message: json['message'],
    comment: Comment.fromJson(json['comment']),
  );
}

class NotificationsResponse {
  final List<dynamic> notifications;
  final int unreadCount;
  final int page;
  final int limit;

  NotificationsResponse({
    required this.notifications,
    required this.unreadCount,
    required this.page,
    required this.limit,
  });

  factory NotificationsResponse.fromJson(Map<String, dynamic> json) => NotificationsResponse(
    notifications: json['notifications'],
    unreadCount: json['unread_count'],
    page: json['page'],
    limit: json['limit'],
  );
}

// Dio配置
class DioConfig {
  static Dio createDio() {
    final dio = Dio();
    
    // 添加拦截器
    dio.interceptors.add(AuthInterceptor());
    dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
    ));
    
    return dio;
  }
}

// 认证拦截器
class AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final token = StorageService.getToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }
}

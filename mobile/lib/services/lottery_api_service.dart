import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:social_app/models/lottery_models.dart';

part 'lottery_api_service.g.dart';

@RestApi()
abstract class LotteryApiService {
  factory LotteryApiService(Dio dio, {String baseUrl}) = _LotteryApiService;

  @GET('/lottery/types')
  Future<LotteryTypesResponse> getLotteryTypes();

  @GET('/lottery/predictions/public')
  Future<PredictionsResponse> getPublicPredictions({
    @Query('lottery_type_id') int? lotteryTypeId,
    @Query('page') int page = 1,
    @Query('limit') int limit = 20,
  });

  @GET('/lottery/predictions/my')
  Future<PredictionsResponse> getMyPredictions({
    @Query('page') int page = 1,
    @Query('limit') int limit = 20,
  });

  @GET('/lottery/predictions/{id}')
  Future<Prediction> getPredictionDetail(@Path('id') int id);

  @POST('/lottery/ai/predict')
  Future<PredictionResponse> generateAIPrediction(
    @Body() AIPredictionRequest request,
  );

  @POST('/lottery/predictions/{id}/like')
  Future<dynamic> likePrediction(@Path('id') int id);

  @DELETE('/lottery/predictions/{id}/like')
  Future<dynamic> unlikePrediction(@Path('id') int id);

  @POST('/lottery/predictions')
  Future<PredictionResponse> createPrediction(@Body() dynamic data);
}

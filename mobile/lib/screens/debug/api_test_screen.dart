import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:social_app/core/services/api_service.dart';
import 'package:social_app/providers/auth_provider.dart';
import 'package:social_app/models/post.dart';

class ApiTestScreen extends ConsumerStatefulWidget {
  const ApiTestScreen({super.key});

  @override
  ConsumerState<ApiTestScreen> createState() => _ApiTestScreenState();
}

class _ApiTestScreenState extends ConsumerState<ApiTestScreen> {
  final List<String> _logs = [];
  bool _isLoading = false;

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)}: $message');
    });
  }

  Future<void> _testHealthCheck() async {
    setState(() => _isLoading = true);
    _addLog('开始健康检查测试...');
    
    try {
      final apiService = ref.read(apiServiceProvider);
      final dio = DioConfig.createDio();
      final response = await dio.get('http://localhost:8080/api/v1/health');
      _addLog('✅ 健康检查成功: ${response.data}');
    } catch (e) {
      _addLog('❌ 健康检查失败: $e');
    }
    
    setState(() => _isLoading = false);
  }

  Future<void> _testUserRegistration() async {
    setState(() => _isLoading = true);
    _addLog('开始用户注册测试...');
    
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      await ref.read(authProvider.notifier).register(
        username: 'testuser$timestamp',
        email: 'test$<EMAIL>',
        password: 'password123',
        displayName: 'Test User $timestamp',
      );
      _addLog('✅ 用户注册成功');
      
      final authState = ref.read(authProvider);
      if (authState.user != null) {
        _addLog('✅ 用户信息: ${authState.user!.displayName}');
      }
    } catch (e) {
      _addLog('❌ 用户注册失败: $e');
    }
    
    setState(() => _isLoading = false);
  }

  Future<void> _testGetCurrentUser() async {
    setState(() => _isLoading = true);
    _addLog('开始获取当前用户测试...');
    
    try {
      final apiService = ref.read(apiServiceProvider);
      final userProfile = await apiService.getCurrentUser();
      _addLog('✅ 获取用户信息成功');
      _addLog('   用户名: ${userProfile.user.username}');
      _addLog('   显示名: ${userProfile.user.displayName}');
      _addLog('   关注者: ${userProfile.followerCount}');
      _addLog('   关注中: ${userProfile.followingCount}');
    } catch (e) {
      _addLog('❌ 获取用户信息失败: $e');
    }
    
    setState(() => _isLoading = false);
  }

  Future<void> _testCreatePost() async {
    setState(() => _isLoading = true);
    _addLog('开始创建动态测试...');
    
    try {
      final apiService = ref.read(apiServiceProvider);
      final request = CreatePostRequest(
        content: '这是一条来自Flutter应用的测试动态！时间：${DateTime.now()}',
        isPublic: true,
      );
      
      final response = await apiService.createPost(request);
      _addLog('✅ 创建动态成功');
      _addLog('   动态ID: ${response.post.id}');
      _addLog('   内容: ${response.post.content}');
    } catch (e) {
      _addLog('❌ 创建动态失败: $e');
    }
    
    setState(() => _isLoading = false);
  }

  Future<void> _testGetFeed() async {
    setState(() => _isLoading = true);
    _addLog('开始获取动态流测试...');
    
    try {
      final apiService = ref.read(apiServiceProvider);
      final response = await apiService.getFeed();
      _addLog('✅ 获取动态流成功');
      _addLog('   动态数量: ${response.posts.length}');
      
      for (int i = 0; i < response.posts.length && i < 3; i++) {
        final post = response.posts[i];
        _addLog('   动态${i + 1}: ${post.content.substring(0, 30)}...');
      }
    } catch (e) {
      _addLog('❌ 获取动态流失败: $e');
    }
    
    setState(() => _isLoading = false);
  }

  void _clearLogs() {
    setState(() {
      _logs.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('API测试'),
        actions: [
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: _clearLogs,
            tooltip: '清除日志',
          ),
        ],
      ),
      body: Column(
        children: [
          // 测试按钮区域
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _testHealthCheck,
                        child: const Text('健康检查'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _testUserRegistration,
                        child: const Text('用户注册'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _testGetCurrentUser,
                        child: const Text('获取用户'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _testCreatePost,
                        child: const Text('创建动态'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testGetFeed,
                    child: const Text('获取动态流'),
                  ),
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // 日志显示区域
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Text(
                        '测试日志',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      if (_isLoading)
                        const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: _logs.isEmpty
                          ? const Center(
                              child: Text(
                                '点击上方按钮开始API测试',
                                style: TextStyle(color: Colors.grey),
                              ),
                            )
                          : ListView.builder(
                              itemCount: _logs.length,
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 2),
                                  child: Text(
                                    _logs[index],
                                    style: const TextStyle(
                                      fontFamily: 'monospace',
                                      fontSize: 12,
                                    ),
                                  ),
                                );
                              },
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

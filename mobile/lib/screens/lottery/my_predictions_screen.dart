import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:social_app/models/lottery_models.dart';
import 'package:social_app/providers/lottery_provider.dart';
import 'package:social_app/services/lottery_api_service.dart';

// 我的预测状态管理
final myPredictionsProvider =
    StateNotifierProvider<MyPredictionsNotifier, PredictionsState>((ref) {
  final apiService = ref.watch(lotteryApiServiceProvider);
  return MyPredictionsNotifier(apiService);
});

class MyPredictionsNotifier extends StateNotifier<PredictionsState> {
  final LotteryApiService _apiService;

  MyPredictionsNotifier(this._apiService) : super(PredictionsState());

  Future<void> loadMyPredictions({bool refresh = false}) async {
    if (refresh) {
      state = state.copyWith(
        predictions: [],
        currentPage: 1,
        hasMore: true,
        error: null,
      );
    }

    if (state.isLoading || !state.hasMore) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _apiService.getMyPredictions(
        page: state.currentPage,
        limit: 20,
      );

      final newPredictions = refresh 
          ? response.predictions
          : [...state.predictions, ...response.predictions];

      state = state.copyWith(
        predictions: newPredictions,
        isLoading: false,
        currentPage: state.currentPage + 1,
        hasMore: response.predictions.length >= 20,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }
}

class MyPredictionsScreen extends ConsumerStatefulWidget {
  const MyPredictionsScreen({super.key});

  @override
  ConsumerState<MyPredictionsScreen> createState() => _MyPredictionsScreenState();
}

class _MyPredictionsScreenState extends ConsumerState<MyPredictionsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(myPredictionsProvider.notifier).loadMyPredictions(refresh: true);
    });
  }

  @override
  Widget build(BuildContext context) {
    final predictionsState = ref.watch(myPredictionsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('我的预测'),
        backgroundColor: Colors.purple.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.read(myPredictionsProvider.notifier).loadMyPredictions(refresh: true);
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.purple.shade600,
              Colors.purple.shade400,
              Colors.white,
            ],
            stops: const [0.0, 0.3, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 统计卡片
              _buildStatsCard(predictionsState.predictions),
              
              // 预测列表
              Expanded(
                child: _buildPredictionsList(predictionsState),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatsCard(List<Prediction> predictions) {
    final totalPredictions = predictions.length;
    final winPredictions = predictions.where((p) => p.isWin == true).length;
    final winRate = totalPredictions > 0 ? (winPredictions / totalPredictions * 100) : 0.0;
    final avgConfidence = totalPredictions > 0 
        ? predictions.map((p) => p.confidence).reduce((a, b) => a + b) / totalPredictions * 100
        : 0.0;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics,
                color: Colors.purple.shade600,
                size: 28,
              ),
              const SizedBox(width: 12),
              const Text(
                '预测统计',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('总预测', '$totalPredictions', Icons.casino),
              _buildStatItem('命中率', '${winRate.toStringAsFixed(1)}%', Icons.trending_up),
              _buildStatItem('平均置信度', '${avgConfidence.toStringAsFixed(1)}%', Icons.psychology),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: Colors.purple.shade600,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.purple.shade600,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildPredictionsList(PredictionsState state) {
    if (state.isLoading && state.predictions.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              state.error!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                ref.read(myPredictionsProvider.notifier).loadMyPredictions(refresh: true);
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (state.predictions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              '暂无预测记录',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '快去生成一些预测吧！',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: state.predictions.length + (state.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == state.predictions.length) {
          return Container(
            padding: const EdgeInsets.all(16),
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        final prediction = state.predictions[index];
        return _buildPredictionCard(prediction);
      },
    );
  }

  Widget _buildPredictionCard(Prediction prediction) {
    final isWin = prediction.isWin;
    final statusColor = isWin == null 
        ? Colors.orange 
        : isWin 
            ? Colors.green 
            : Colors.red;
    final statusText = isWin == null 
        ? '待开奖' 
        : isWin 
            ? '已中奖' 
            : '未中奖';

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 头部信息
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: statusColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  statusText,
                  style: TextStyle(
                    fontSize: 12,
                    color: statusColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                prediction.lotteryType?.name ?? '未知彩票',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                '${prediction.period}期',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // 号码显示
          Row(
            children: [
              // 红球
              ...prediction.redBallsList.map((ball) {
                return Container(
                  margin: const EdgeInsets.only(right: 6),
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    color: Colors.red.shade600,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      ball,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                );
              }),
              
              // 分隔符（如果有蓝球）
              if (prediction.blueBallsList.isNotEmpty) ...[
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 6),
                  width: 1,
                  height: 16,
                  color: Colors.grey.shade400,
                ),
                
                // 蓝球
                ...prediction.blueBallsList.map((ball) {
                  return Container(
                    margin: const EdgeInsets.only(right: 6),
                    width: 28,
                    height: 28,
                    decoration: BoxDecoration(
                      color: Colors.blue.shade600,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        ball,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  );
                }),
              ],
            ],
          ),
          
          const SizedBox(height: 16),
          
          // 底部信息
          Row(
            children: [
              Text(
                '置信度: ${(prediction.confidence * 100).toStringAsFixed(1)}%',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
              const Spacer(),
              if (prediction.winAmount > 0)
                Text(
                  '奖金: ¥${prediction.winAmount}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.green.shade600,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              const SizedBox(width: 8),
              Text(
                prediction.createdAt.toString().substring(0, 16),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:social_app/models/lottery_models.dart';
import 'package:social_app/providers/lottery_provider.dart';

class FC3DPredictionScreen extends ConsumerStatefulWidget {
  const FC3DPredictionScreen({super.key});

  @override
  ConsumerState<FC3DPredictionScreen> createState() => _FC3DPredictionScreenState();
}

class _FC3DPredictionScreenState extends ConsumerState<FC3DPredictionScreen> {
  bool _showDigitalAvatar = false;
  String _currentAnnouncementText = '欢迎来到AI彩票预测！点击生成预测后，我将为您播报结果。';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(lotteryTypesProvider.notifier).loadLotteryTypes();
    });
  }

  @override
  Widget build(BuildContext context) {
    final lotteryTypesState = ref.watch(lotteryTypesProvider);
    final aiPredictionState = ref.watch(aiPredictionProvider);
    final fc3dType = lotteryTypesState.lotteryTypes
        .where((type) => type.code == 'fc3d')
        .firstOrNull;

    return Scaffold(
      appBar: AppBar(
        title: const Text('福彩3D AI预测'),
        backgroundColor: Colors.green.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.green.shade600,
              Colors.green.shade400,
              Colors.white,
            ],
            stops: const [0.0, 0.3, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 数字人播报区域（上半部分）
              Expanded(
                flex: 2,
                child: _buildDigitalAvatarSection(),
              ),

              // 预测页面内容（下半部分）
              Expanded(
                flex: 3,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, -5),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // 播报文字区域
                      if (_showDigitalAvatar && aiPredictionState.prediction != null)
                        _buildAnnouncementTextArea(),

                      if (fc3dType != null) _buildLotteryInfoCard(fc3dType),
                      if (aiPredictionState.prediction != null)
                        _buildPredictionResult(aiPredictionState.prediction!),
                      _buildGenerateButton(fc3dType, aiPredictionState.isLoading),
                      Expanded(child: _buildHistoryPredictions()),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLotteryInfoCard(LotteryType lotteryType) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.view_in_ar,
                color: Colors.green.shade600,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                lotteryType.name,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            lotteryType.description,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildInfoChip('号码', '${lotteryType.redBalls}位', Colors.green),
              const SizedBox(width: 8),
              _buildInfoChip('范围', lotteryType.redRange, Colors.grey),
              const SizedBox(width: 8),
              _buildInfoChip('开奖', '每日', Colors.orange),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        '$label: $value',
        style: TextStyle(
          fontSize: 12,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildPredictionResult(Prediction prediction) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.auto_awesome,
                color: Colors.amber.shade600,
                size: 24,
              ),
              const SizedBox(width: 8),
              const Text(
                'AI智能预测',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '置信度: ${(prediction.confidence * 100).toStringAsFixed(1)}%',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.green.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // 3D号码显示
          const Text(
            '预测号码',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: prediction.redBallsList.map((ball) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 8),
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.green.shade600,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.green.shade300,
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Center(
                  child: Text(
                    ball,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 20,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          
          const SizedBox(height: 16),
          
          // 预测信息
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '期号: ${prediction.period}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
              Text(
                '方法: ${prediction.method.toUpperCase()}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGenerateButton(LotteryType? lotteryType, bool isLoading) {
    return Container(
      margin: const EdgeInsets.all(16),
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: lotteryType != null && !isLoading
            ? () => _generatePrediction(lotteryType)
            : null,
        icon: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Icon(Icons.auto_awesome),
        label: Text(isLoading ? '生成中...' : '生成AI预测'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green.shade600,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildHistoryPredictions() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '历史预测',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Center(
                child: Text(
                  '暂无历史预测记录\n生成预测后将在这里显示',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _generatePrediction(LotteryType lotteryType) {
    final now = DateTime.now();
    final year = now.year;
    final dayOfYear = now.difference(DateTime(year, 1, 1)).inDays + 1;
    final period = '$year${dayOfYear.toString().padLeft(3, '0')}';

    ref.read(aiPredictionProvider.notifier).generatePrediction(
      lotteryType.id,
      period,
    );
  }

  void _toggleDigitalAvatar() {
    setState(() {
      _showDigitalAvatar = !_showDigitalAvatar;
    });

    // 如果开启播报，自动开始播报
    if (_showDigitalAvatar) {
      _startIntegratedAnnouncement();
    } else {
      setState(() {
        _currentAnnouncementText = '欢迎来到AI彩票预测！点击生成预测后，我将为您播报结果。';
      });
    }
  }

  void _startIntegratedAnnouncement() async {
    final aiPredictionState = ref.read(aiPredictionProvider);
    if (aiPredictionState.prediction == null) {
      setState(() {
        _currentAnnouncementText = '请先生成预测，然后我将为您播报结果！';
      });
      return;
    }

    final prediction = aiPredictionState.prediction!;
    final lotteryType = prediction.lotteryType?.name ?? '彩票';
    final confidence = (prediction.confidence * 100).toStringAsFixed(1);

    // 播报流程
    final announcements = [
      '大家好！我是AI预测助手小彩！',
      '让我为您播报最新的预测结果！',
      '这次的预测置信度很不错哦！',
      '本次为您预测的是$lotteryType，置信度为$confidence%',
      '预测的3D号码是：${prediction.redBallsList.join(' - ')}',
      '祝您好运！记得理性购彩哦！',
    ];

    for (int i = 0; i < announcements.length; i++) {
      if (!_showDigitalAvatar) break; // 如果用户关闭了播报，停止

      setState(() {
        _currentAnnouncementText = announcements[i];
      });

      await Future.delayed(const Duration(milliseconds: 2500));
    }
  }

  Widget _buildDigitalAvatarSection() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.green.shade800,
            Colors.green.shade600,
            Colors.green.shade400,
          ],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // 顶部按钮区域
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 左侧：数字人播报按钮
                  ElevatedButton.icon(
                    onPressed: () => _toggleDigitalAvatar(),
                    icon: Icon(
                      _showDigitalAvatar ? Icons.stop : Icons.record_voice_over,
                      size: 18,
                    ),
                    label: Text(_showDigitalAvatar ? '停止播报' : '开始播报'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _showDigitalAvatar ? Colors.red.shade600 : Colors.white,
                      foregroundColor: _showDigitalAvatar ? Colors.white : Colors.green.shade800,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      textStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                  ),

                  // 右侧：小彩标识
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: const Text(
                      '小彩',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 数字人头像（占据剩余空间）
            Expanded(
              child: Center(
                child: _buildAvatar(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnnouncementTextArea() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.record_voice_over, color: Colors.green.shade600),
              const SizedBox(width: 8),
              Text(
                '数字人小彩播报中...',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.green.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            _currentAnnouncementText,
            style: const TextStyle(
              fontSize: 14,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 200,
      height: 200,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.cyan.shade300,
            Colors.blue.shade400,
            Colors.purple.shade400,
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.white.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 头部
          Positioned(
            top: 30,
            left: 50,
            right: 50,
            child: Container(
              height: 80,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(40),
              ),
            ),
          ),

          // 眼睛
          Positioned(
            top: 50,
            left: 70,
            child: _buildEye(),
          ),
          Positioned(
            top: 50,
            right: 70,
            child: _buildEye(),
          ),

          // 嘴巴
          Positioned(
            top: 90,
            left: 80,
            right: 80,
            child: Container(
              height: 20,
              decoration: BoxDecoration(
                color: Colors.pink.shade300,
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),

          // 装饰元素
          Positioned(
            top: 10,
            right: 20,
            child: Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                color: Colors.yellow.shade400,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.auto_awesome,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEye() {
    return Container(
      width: 20,
      height: 20,
      decoration: const BoxDecoration(
        color: Colors.black87,
        shape: BoxShape.circle,
      ),
      child: Container(
        margin: const EdgeInsets.all(4),
        decoration: const BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:social_app/models/lottery_models.dart';
import 'package:social_app/providers/lottery_provider.dart';

class PredictionSquareScreen extends ConsumerStatefulWidget {
  const PredictionSquareScreen({super.key});

  @override
  ConsumerState<PredictionSquareScreen> createState() => _PredictionSquareScreenState();
}

class _PredictionSquareScreenState extends ConsumerState<PredictionSquareScreen> {
  int? selectedLotteryTypeId;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(lotteryTypesProvider.notifier).loadLotteryTypes();
      ref.read(publicPredictionsProvider.notifier).loadPredictions(refresh: true);
    });
  }

  @override
  Widget build(BuildContext context) {
    final lotteryTypesState = ref.watch(lotteryTypesProvider);
    final predictionsState = ref.watch(publicPredictionsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('预测广场'),
        backgroundColor: Colors.orange.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.orange.shade600,
              Colors.orange.shade400,
              Colors.white,
            ],
            stops: const [0.0, 0.3, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 筛选器
              _buildFilterSection(lotteryTypesState.lotteryTypes),
              
              // 预测列表
              Expanded(
                child: _buildPredictionsList(predictionsState),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterSection(List<LotteryType> lotteryTypes) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '筛选彩票类型',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildFilterChip('全部', null),
              ...lotteryTypes.map((type) => _buildFilterChip(type.name, type.id)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, int? lotteryTypeId) {
    final isSelected = selectedLotteryTypeId == lotteryTypeId;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          selectedLotteryTypeId = selected ? lotteryTypeId : null;
        });
        ref.read(publicPredictionsProvider.notifier).loadPredictions(
          lotteryTypeId: selectedLotteryTypeId,
          refresh: true,
        );
      },
      selectedColor: Colors.orange.shade100,
      checkmarkColor: Colors.orange.shade600,
      labelStyle: TextStyle(
        color: isSelected ? Colors.orange.shade600 : Colors.grey.shade700,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }

  Widget _buildPredictionsList(PredictionsState state) {
    if (state.isLoading && state.predictions.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              state.error!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                ref.read(publicPredictionsProvider.notifier).loadPredictions(
                  lotteryTypeId: selectedLotteryTypeId,
                  refresh: true,
                );
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (state.predictions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              '暂无预测记录',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '快去生成一些预测吧！',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: state.predictions.length + (state.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == state.predictions.length) {
          // 加载更多指示器
          return Container(
            padding: const EdgeInsets.all(16),
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        final prediction = state.predictions[index];
        return _buildPredictionCard(prediction);
      },
    );
  }

  Widget _buildPredictionCard(Prediction prediction) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 头部信息
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: Colors.orange.shade100,
                child: Text(
                  prediction.user?.displayName.substring(0, 1).toUpperCase() ?? 'A',
                  style: TextStyle(
                    color: Colors.orange.shade600,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      prediction.user?.displayName ?? '匿名用户',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${prediction.lotteryType?.name ?? '未知彩票'} · ${prediction.period}期',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${(prediction.confidence * 100).toStringAsFixed(1)}%',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.green.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // 号码显示
          Row(
            children: [
              // 红球
              ...prediction.redBallsList.map((ball) {
                return Container(
                  margin: const EdgeInsets.only(right: 6),
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    color: Colors.red.shade600,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      ball,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                );
              }),
              
              // 分隔符（如果有蓝球）
              if (prediction.blueBallsList.isNotEmpty) ...[
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 6),
                  width: 1,
                  height: 16,
                  color: Colors.grey.shade400,
                ),
                
                // 蓝球
                ...prediction.blueBallsList.map((ball) {
                  return Container(
                    margin: const EdgeInsets.only(right: 6),
                    width: 28,
                    height: 28,
                    decoration: BoxDecoration(
                      color: Colors.blue.shade600,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        ball,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  );
                }),
              ],
            ],
          ),
          
          const SizedBox(height: 16),
          
          // 底部操作
          Row(
            children: [
              Text(
                '${prediction.method.toUpperCase()}算法',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
              const Spacer(),
              _buildActionButton(
                Icons.thumb_up_outlined,
                '${prediction.likeCount}',
                Colors.blue,
                () => _likePrediction(prediction.id),
              ),
              const SizedBox(width: 16),
              _buildActionButton(
                Icons.people_outlined,
                '${prediction.followCount}',
                Colors.green,
                () => _followPrediction(prediction.id),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    IconData icon,
    String text,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: color,
            ),
            const SizedBox(width: 4),
            Text(
              text,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _likePrediction(int predictionId) {
    // TODO: 实现点赞功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('点赞功能即将上线'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _followPrediction(int predictionId) {
    // TODO: 实现跟单功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('跟单功能即将上线'),
        backgroundColor: Colors.green,
      ),
    );
  }
}

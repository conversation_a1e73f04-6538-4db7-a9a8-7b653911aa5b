import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:social_app/models/lottery_models.dart';
import 'package:social_app/providers/lottery_provider.dart';

// 排行榜数据模型
class RankingUser {
  final int id;
  final String displayName;
  final String avatar;
  final int totalPredictions;
  final int winPredictions;
  final double winRate;
  final int totalWinAmount;
  final double avgConfidence;
  final int rank;

  RankingUser({
    required this.id,
    required this.displayName,
    required this.avatar,
    required this.totalPredictions,
    required this.winPredictions,
    required this.winRate,
    required this.totalWinAmount,
    required this.avgConfidence,
    required this.rank,
  });
}

// 排行榜状态
class RankingState {
  final List<RankingUser> users;
  final bool isLoading;
  final String? error;
  final String currentTab;

  RankingState({
    this.users = const [],
    this.isLoading = false,
    this.error,
    this.currentTab = 'winRate',
  });

  RankingState copyWith({
    List<RankingUser>? users,
    bool? isLoading,
    String? error,
    String? currentTab,
  }) {
    return RankingState(
      users: users ?? this.users,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      currentTab: currentTab ?? this.currentTab,
    );
  }
}

// 排行榜Notifier
class RankingNotifier extends StateNotifier<RankingState> {
  RankingNotifier() : super(RankingState());

  Future<void> loadRanking(String type) async {
    state = state.copyWith(isLoading: true, error: null, currentTab: type);

    try {
      // 模拟API调用 - 实际应该调用真实的排行榜API
      await Future.delayed(const Duration(seconds: 1));
      
      final mockUsers = _generateMockRankingData(type);
      
      state = state.copyWith(
        users: mockUsers,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  List<RankingUser> _generateMockRankingData(String type) {
    final names = [
      '预测大师', '彩票专家', '幸运星', '数字猎人', '概率王',
      '算法达人', '预测之神', '中奖王', '分析师', '彩票高手',
      '数据专家', '预测精英', '统计大师', '概率专家', '彩票达人'
    ];

    return List.generate(15, (index) {
      final totalPredictions = 50 + (index * 10);
      final winPredictions = (totalPredictions * (0.3 + index * 0.02)).round();
      final winRate = winPredictions / totalPredictions;
      
      return RankingUser(
        id: index + 1,
        displayName: names[index],
        avatar: '',
        totalPredictions: totalPredictions,
        winPredictions: winPredictions,
        winRate: winRate,
        totalWinAmount: (winPredictions * 100 * (1 + index * 0.5)).round(),
        avgConfidence: 0.25 + (index * 0.02),
        rank: index + 1,
      );
    });
  }
}

// 排行榜Provider
final rankingProvider = StateNotifierProvider<RankingNotifier, RankingState>((ref) {
  return RankingNotifier();
});

class RankingScreen extends ConsumerStatefulWidget {
  const RankingScreen({super.key});

  @override
  ConsumerState<RankingScreen> createState() => _RankingScreenState();
}

class _RankingScreenState extends ConsumerState<RankingScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(rankingProvider.notifier).loadRanking('winRate');
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final rankingState = ref.watch(rankingProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('排行榜'),
        backgroundColor: Colors.amber.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          onTap: (index) {
            final types = ['winRate', 'totalWin', 'predictions'];
            ref.read(rankingProvider.notifier).loadRanking(types[index]);
          },
          tabs: const [
            Tab(text: '命中率榜'),
            Tab(text: '奖金榜'),
            Tab(text: '预测榜'),
          ],
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.amber.shade600,
              Colors.amber.shade400,
              Colors.white,
            ],
            stops: const [0.0, 0.3, 1.0],
          ),
        ),
        child: SafeArea(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildRankingList(rankingState, 'winRate'),
              _buildRankingList(rankingState, 'totalWin'),
              _buildRankingList(rankingState, 'predictions'),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRankingList(RankingState state, String type) {
    if (state.isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                ref.read(rankingProvider.notifier).loadRanking(type);
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: state.users.length,
      itemBuilder: (context, index) {
        final user = state.users[index];
        return _buildRankingCard(user, type, index);
      },
    );
  }

  Widget _buildRankingCard(RankingUser user, String type, int index) {
    final isTopThree = index < 3;
    final rankColors = [Colors.amber, Colors.grey, Colors.brown];
    final rankIcons = [Icons.looks_one, Icons.looks_two, Icons.looks_3];

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: isTopThree 
            ? Border.all(color: rankColors[index], width: 2)
            : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          // 排名
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isTopThree ? rankColors[index] : Colors.grey.shade300,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: isTopThree
                  ? Icon(
                      rankIcons[index],
                      color: Colors.white,
                      size: 24,
                    )
                  : Text(
                      '${user.rank}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // 用户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      user.displayName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (isTopThree) ...[
                      const SizedBox(width: 8),
                      Icon(
                        Icons.verified,
                        color: rankColors[index],
                        size: 16,
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  _getSubtitle(user, type),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          
          // 主要数据
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                _getMainValue(user, type),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isTopThree ? rankColors[index] : Colors.black87,
                ),
              ),
              Text(
                _getMainLabel(type),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getMainValue(RankingUser user, String type) {
    switch (type) {
      case 'winRate':
        return '${(user.winRate * 100).toStringAsFixed(1)}%';
      case 'totalWin':
        return '¥${user.totalWinAmount}';
      case 'predictions':
        return '${user.totalPredictions}';
      default:
        return '';
    }
  }

  String _getMainLabel(String type) {
    switch (type) {
      case 'winRate':
        return '命中率';
      case 'totalWin':
        return '总奖金';
      case 'predictions':
        return '预测数';
      default:
        return '';
    }
  }

  String _getSubtitle(RankingUser user, String type) {
    switch (type) {
      case 'winRate':
        return '${user.winPredictions}/${user.totalPredictions} 中奖';
      case 'totalWin':
        return '命中率 ${(user.winRate * 100).toStringAsFixed(1)}%';
      case 'predictions':
        return '命中率 ${(user.winRate * 100).toStringAsFixed(1)}%';
      default:
        return '';
    }
  }
}

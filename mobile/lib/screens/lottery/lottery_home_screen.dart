import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:social_app/screens/lottery/ssq_prediction_screen.dart';
import 'package:social_app/screens/lottery/dlt_prediction_screen.dart';
import 'package:social_app/screens/lottery/fc3d_prediction_screen.dart';
import 'package:social_app/screens/lottery/prediction_square_screen.dart';
import 'package:social_app/screens/lottery/my_predictions_screen.dart';
import 'package:social_app/screens/lottery/ranking_screen.dart';

class LotteryHomeScreen extends ConsumerStatefulWidget {
  const LotteryHomeScreen({super.key});

  @override
  ConsumerState<LotteryHomeScreen> createState() => _LotteryHomeScreenState();
}

class _LotteryHomeScreenState extends ConsumerState<LotteryHomeScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI彩票预测'),
        backgroundColor: Colors.purple.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.purple.shade600,
              Colors.purple.shade400,
              Colors.white,
            ],
            stops: const [0.0, 0.3, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 头部统计卡片
              Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.auto_awesome,
                          color: Colors.purple.shade600,
                          size: 28,
                        ),
                        const SizedBox(width: 12),
                        const Text(
                          'AI智能预测',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatItem('今日预测', '12', Icons.today),
                        _buildStatItem('命中率', '68%', Icons.trending_up),
                        _buildStatItem('跟单数', '156', Icons.people),
                      ],
                    ),
                  ],
                ),
              ),

              // 彩票类型网格
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  child: GridView.count(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: 1.2,
                    children: [
                      _buildLotteryCard(
                        '双色球',
                        'AI智能分析红蓝球',
                        Icons.circle,
                        Colors.red,
                        () => _navigateToLotteryType('ssq'),
                      ),
                      _buildLotteryCard(
                        '大乐透',
                        '超级大乐透预测',
                        Icons.star,
                        Colors.blue,
                        () => _navigateToLotteryType('dlt'),
                      ),
                      _buildLotteryCard(
                        '福彩3D',
                        '3D号码智能推荐',
                        Icons.view_in_ar,
                        Colors.green,
                        () => _navigateToLotteryType('fc3d'),
                      ),
                      _buildLotteryCard(
                        '预测广场',
                        '查看所有公开预测',
                        Icons.public,
                        Colors.orange,
                        () => _navigateToPredictionSquare(),
                      ),
                    ],
                  ),
                ),
              ),

              // 底部快捷操作
              Container(
                margin: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _navigateToMyPredictions,
                        icon: const Icon(Icons.history),
                        label: const Text('我的预测'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.purple.shade600,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _navigateToRanking,
                        icon: const Icon(Icons.leaderboard),
                        label: const Text('排行榜'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.amber.shade600,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: Colors.purple.shade600,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.purple.shade600,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildLotteryCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withOpacity(0.1),
                color.withOpacity(0.05),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 40,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToLotteryType(String lotteryCode) {
    Widget? screen;

    switch (lotteryCode) {
      case 'ssq':
        screen = const SSQPredictionScreen();
        break;
      case 'dlt':
        screen = const DLTPredictionScreen();
        break;
      case 'fc3d':
        screen = const FC3DPredictionScreen();
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$lotteryCode 预测功能即将上线'),
            backgroundColor: Colors.purple.shade600,
          ),
        );
        return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => screen!),
    );
  }

  void _navigateToPredictionSquare() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const PredictionSquareScreen(),
      ),
    );
  }

  void _navigateToMyPredictions() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const MyPredictionsScreen(),
      ),
    );
  }

  void _navigateToRanking() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const RankingScreen(),
      ),
    );
  }
}

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:social_app/models/lottery_models.dart';
import 'package:social_app/services/lottery_api_service.dart';
import 'package:social_app/providers/api_service_provider.dart';

// 彩票API服务提供者
final lotteryApiServiceProvider = Provider<LotteryApiService>((ref) {
  final dio = ref.watch(dioProvider);
  return LotteryApiService(dio, baseUrl: 'http://localhost:8080/api/v1');
});

// 彩票类型状态
class LotteryTypesState {
  final List<LotteryType> lotteryTypes;
  final bool isLoading;
  final String? error;

  LotteryTypesState({
    this.lotteryTypes = const [],
    this.isLoading = false,
    this.error,
  });

  LotteryTypesState copyWith({
    List<LotteryType>? lotteryTypes,
    bool? isLoading,
    String? error,
  }) {
    return LotteryTypesState(
      lotteryTypes: lotteryTypes ?? this.lotteryTypes,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// 彩票类型Notifier
class LotteryTypesNotifier extends StateNotifier<LotteryTypesState> {
  final LotteryApiService _apiService;

  LotteryTypesNotifier(this._apiService) : super(LotteryTypesState());

  Future<void> loadLotteryTypes() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final response = await _apiService.getLotteryTypes();
      state = state.copyWith(
        lotteryTypes: response.lotteryTypes,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }
}

// 彩票类型提供者
final lotteryTypesProvider =
    StateNotifierProvider<LotteryTypesNotifier, LotteryTypesState>((ref) {
  final apiService = ref.watch(lotteryApiServiceProvider);
  return LotteryTypesNotifier(apiService);
});

// 预测状态
class PredictionsState {
  final List<Prediction> predictions;
  final bool isLoading;
  final String? error;
  final int currentPage;
  final bool hasMore;

  PredictionsState({
    this.predictions = const [],
    this.isLoading = false,
    this.error,
    this.currentPage = 1,
    this.hasMore = true,
  });

  PredictionsState copyWith({
    List<Prediction>? predictions,
    bool? isLoading,
    String? error,
    int? currentPage,
    bool? hasMore,
  }) {
    return PredictionsState(
      predictions: predictions ?? this.predictions,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      currentPage: currentPage ?? this.currentPage,
      hasMore: hasMore ?? this.hasMore,
    );
  }
}

// 公开预测Notifier
class PublicPredictionsNotifier extends StateNotifier<PredictionsState> {
  final LotteryApiService _apiService;

  PublicPredictionsNotifier(this._apiService) : super(PredictionsState());

  Future<void> loadPredictions({int? lotteryTypeId, bool refresh = false}) async {
    if (refresh) {
      state = state.copyWith(
        predictions: [],
        currentPage: 1,
        hasMore: true,
        error: null,
      );
    }

    if (state.isLoading || !state.hasMore) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _apiService.getPublicPredictions(
        lotteryTypeId: lotteryTypeId,
        page: state.currentPage,
        limit: 20,
      );

      final newPredictions = refresh 
          ? response.predictions
          : [...state.predictions, ...response.predictions];

      state = state.copyWith(
        predictions: newPredictions,
        isLoading: false,
        currentPage: state.currentPage + 1,
        hasMore: response.predictions.length >= 20,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }
}

// 公开预测提供者
final publicPredictionsProvider =
    StateNotifierProvider<PublicPredictionsNotifier, PredictionsState>((ref) {
  final apiService = ref.watch(lotteryApiServiceProvider);
  return PublicPredictionsNotifier(apiService);
});

// AI预测状态
class AIPredictionState {
  final Prediction? prediction;
  final bool isLoading;
  final String? error;

  AIPredictionState({
    this.prediction,
    this.isLoading = false,
    this.error,
  });

  AIPredictionState copyWith({
    Prediction? prediction,
    bool? isLoading,
    String? error,
  }) {
    return AIPredictionState(
      prediction: prediction ?? this.prediction,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// AI预测Notifier
class AIPredictionNotifier extends StateNotifier<AIPredictionState> {
  final LotteryApiService _apiService;

  AIPredictionNotifier(this._apiService) : super(AIPredictionState());

  Future<void> generatePrediction(int lotteryTypeId, String period) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final request = AIPredictionRequest(
        lotteryTypeId: lotteryTypeId,
        period: period,
      );
      
      final response = await _apiService.generateAIPrediction(request);
      
      state = state.copyWith(
        prediction: response.prediction,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  void clearPrediction() {
    state = AIPredictionState();
  }
}

// AI预测提供者
final aiPredictionProvider =
    StateNotifierProvider<AIPredictionNotifier, AIPredictionState>((ref) {
  final apiService = ref.watch(lotteryApiServiceProvider);
  return AIPredictionNotifier(apiService);
});

// 根据代码获取彩票类型
final lotteryTypeByCodeProvider = Provider.family<LotteryType?, String>((ref, code) {
  final lotteryTypesState = ref.watch(lotteryTypesProvider);
  return lotteryTypesState.lotteryTypes
      .where((type) => type.code == code)
      .firstOrNull;
});

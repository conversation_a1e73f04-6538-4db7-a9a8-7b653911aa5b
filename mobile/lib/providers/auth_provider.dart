import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:social_app/core/services/api_service.dart';
import 'package:social_app/core/services/storage_service.dart';
import 'package:social_app/models/user.dart';

// 认证状态
class AuthState {
  final bool isLoading;
  final User? user;
  final String? error;

  const AuthState({
    this.isLoading = false,
    this.user,
    this.error,
  });

  AuthState copyWith({
    bool? isLoading,
    User? user,
    String? error,
  }) {
    return AuthState(
      isLoading: isLoading ?? this.isLoading,
      user: user ?? this.user,
      error: error,
    );
  }

  bool get isAuthenticated => user != null;
}

// API服务提供者
final apiServiceProvider = Provider<ApiService>((ref) {
  final dio = DioConfig.createDio();
  return ApiService(dio);
});

// 认证状态管理
class AuthNotifier extends StateNotifier<AuthState> {
  final ApiService _apiService;

  AuthNotifier(this._apiService) : super(const AuthState()) {
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    if (StorageService.isLoggedIn()) {
      try {
        final userProfile = await _apiService.getCurrentUser();
        state = state.copyWith(user: userProfile.user);
      } catch (e) {
        // Token可能已过期，清除本地存储
        await logout();
      }
    }
  }

  Future<void> login(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _apiService.login(
        LoginRequest(email: email, password: password),
      );

      await StorageService.saveToken(response.token);
      await StorageService.saveUserInfo(
        userId: response.user.id,
        username: response.user.username,
      );

      state = state.copyWith(
        isLoading: false,
        user: response.user,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  Future<void> register({
    required String username,
    required String email,
    required String password,
    required String displayName,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _apiService.register(
        RegisterRequest(
          username: username,
          email: email,
          password: password,
          displayName: displayName,
        ),
      );

      await StorageService.saveToken(response.token);
      await StorageService.saveUserInfo(
        userId: response.user.id,
        username: response.user.username,
      );

      state = state.copyWith(
        isLoading: false,
        user: response.user,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  Future<void> logout() async {
    await StorageService.clearUserInfo();
    state = const AuthState();
  }

  Future<void> updateProfile({
    required String displayName,
    required String bio,
  }) async {
    if (state.user == null) return;

    state = state.copyWith(isLoading: true);

    try {
      await _apiService.updateProfile(
        UpdateProfileRequest(displayName: displayName, bio: bio),
      );

      // 重新获取用户信息
      final userProfile = await _apiService.getCurrentUser();
      state = state.copyWith(
        isLoading: false,
        user: userProfile.user,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      rethrow;
    }
  }
}

// 认证提供者
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  return AuthNotifier(apiService);
});

import 'package:flutter/material.dart';
import 'package:timeago/timeago.dart' as timeago;

class PostCard extends StatelessWidget {
  const PostCard({super.key});

  @override
  Widget build(BuildContext context) {
    // 模拟数据
    final mockPost = {
      'user': {
        'displayName': '测试用户',
        'username': 'testuser',
        'avatar': null,
      },
      'content': '这是一条测试动态，展示了我们的社交应用的基本功能。支持文字、图片和各种交互功能。',
      'images': <String>[],
      'likeCount': 42,
      'commentCount': 8,
      'createdAt': DateTime.now().subtract(const Duration(hours: 2)),
      'isLiked': false,
    };

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户信息行
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundImage: mockPost['user']['avatar'] != null
                      ? NetworkImage(mockPost['user']['avatar'])
                      : null,
                  child: mockPost['user']['avatar'] == null
                      ? Text(
                          mockPost['user']['displayName'][0].toUpperCase(),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        )
                      : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        mockPost['user']['displayName'],
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        '@${mockPost['user']['username']}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  timeago.format(mockPost['createdAt'], locale: 'zh'),
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.more_vert),
                  onPressed: () {
                    _showPostMenu(context);
                  },
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // 动态内容
            Text(
              mockPost['content'],
              style: const TextStyle(fontSize: 16),
            ),
            
            // 图片（如果有）
            if (mockPost['images'].isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildImageGrid(mockPost['images']),
            ],
            
            const SizedBox(height: 16),
            
            // 交互按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildActionButton(
                  icon: mockPost['isLiked'] ? Icons.favorite : Icons.favorite_border,
                  label: '${mockPost['likeCount']}',
                  color: mockPost['isLiked'] ? Colors.red : null,
                  onTap: () {
                    // TODO: 实现点赞功能
                  },
                ),
                _buildActionButton(
                  icon: Icons.comment_outlined,
                  label: '${mockPost['commentCount']}',
                  onTap: () {
                    // TODO: 实现评论功能
                  },
                ),
                _buildActionButton(
                  icon: Icons.share_outlined,
                  label: '分享',
                  onTap: () {
                    // TODO: 实现分享功能
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    Color? color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 20, color: color),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(color: color),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageGrid(List<String> images) {
    if (images.length == 1) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.network(
          images[0],
          width: double.infinity,
          height: 200,
          fit: BoxFit.cover,
        ),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: images.length == 2 ? 2 : 2,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
        childAspectRatio: 1,
      ),
      itemCount: images.length > 4 ? 4 : images.length,
      itemBuilder: (context, index) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            fit: StackFit.expand,
            children: [
              Image.network(
                images[index],
                fit: BoxFit.cover,
              ),
              if (index == 3 && images.length > 4)
                Container(
                  color: Colors.black54,
                  child: Center(
                    child: Text(
                      '+${images.length - 4}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  void _showPostMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.bookmark_border),
            title: const Text('收藏'),
            onTap: () {
              Navigator.pop(context);
              // TODO: 实现收藏功能
            },
          ),
          ListTile(
            leading: const Icon(Icons.report_outlined),
            title: const Text('举报'),
            onTap: () {
              Navigator.pop(context);
              // TODO: 实现举报功能
            },
          ),
          ListTile(
            leading: const Icon(Icons.block),
            title: const Text('屏蔽用户'),
            onTap: () {
              Navigator.pop(context);
              // TODO: 实现屏蔽功能
            },
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:social_app/models/lottery_models.dart';

class DigitalAvatarAnnouncer extends StatefulWidget {
  final Prediction prediction;
  final VoidCallback? onClose;

  const DigitalAvatarAnnouncer({
    super.key,
    required this.prediction,
    this.onClose,
  });

  @override
  State<DigitalAvatarAnnouncer> createState() => _DigitalAvatarAnnouncerState();
}

class _DigitalAvatarAnnouncerState extends State<DigitalAvatarAnnouncer>
    with TickerProviderStateMixin {
  late AnimationController _avatarController;
  late AnimationController _textController;
  late AnimationController _ballController;
  late Animation<double> _avatarAnimation;
  late Animation<double> _textAnimation;
  late Animation<double> _ballAnimation;
  
  bool _isAnnouncing = false;
  String _currentText = '';
  int _currentStep = 0;

  final List<String> _greetings = [
    '大家好！我是AI预测助手小彩！',
    '让我为您播报最新的预测结果！',
    '这次的预测置信度很不错哦！',
  ];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnnouncement();
  }

  void _setupAnimations() {
    _avatarController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _textController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _ballController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _avatarAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _avatarController,
      curve: Curves.elasticOut,
    ));

    _textAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeInOut,
    ));

    _ballAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _ballController,
      curve: Curves.bounceOut,
    ));
  }

  void _startAnnouncement() async {
    if (!mounted) return;

    setState(() {
      _isAnnouncing = true;
    });

    // 数字人出场动画
    _avatarController.forward();
    await Future.delayed(const Duration(milliseconds: 500));

    // 播报问候语
    for (int i = 0; i < _greetings.length; i++) {
      if (!mounted) return;
      await _announceText(_greetings[i]);
      await Future.delayed(const Duration(milliseconds: 1000));
    }

    // 播报预测结果
    if (!mounted) return;
    await _announcePrediction();

    // 结束语
    if (!mounted) return;
    await _announceText('祝您好运！记得理性购彩哦！');

    if (!mounted) return;
    setState(() {
      _isAnnouncing = false;
    });
  }

  Future<void> _announceText(String text) async {
    if (!mounted) return;

    setState(() {
      _currentText = text;
    });

    _textController.reset();
    _textController.forward();

    // 震动反馈
    HapticFeedback.lightImpact();

    await Future.delayed(const Duration(milliseconds: 2000));
  }

  Future<void> _announcePrediction() async {
    final lotteryType = widget.prediction.lotteryType?.name ?? '彩票';
    final confidence = (widget.prediction.confidence * 100).toStringAsFixed(1);
    
    // 播报彩票类型和置信度
    await _announceText('本次为您预测的是$lotteryType，置信度为$confidence%');
    
    // 播报号码
    if (widget.prediction.lotteryType?.code == 'fc3d') {
      await _announceFC3DNumbers();
    } else {
      await _announceRegularNumbers();
    }
    
    // 播报号码动画
    _ballController.forward();
  }

  Future<void> _announceFC3DNumbers() async {
    final numbers = widget.prediction.redBallsList;
    await _announceText('预测的3D号码是：${numbers.join(' - ')}');
  }

  Future<void> _announceRegularNumbers() async {
    final redBalls = widget.prediction.redBallsList;
    final blueBalls = widget.prediction.blueBallsList;
    
    if (widget.prediction.lotteryType?.code == 'ssq') {
      await _announceText('红球号码：${redBalls.join('、')}');
      await Future.delayed(const Duration(milliseconds: 500));
      await _announceText('蓝球号码：${blueBalls.join('、')}');
    } else if (widget.prediction.lotteryType?.code == 'dlt') {
      await _announceText('前区号码：${redBalls.join('、')}');
      await Future.delayed(const Duration(milliseconds: 500));
      await _announceText('后区号码：${blueBalls.join('、')}');
    }
  }

  @override
  void dispose() {
    _avatarController.dispose();
    _textController.dispose();
    _ballController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
          children: [
            // 关闭按钮
            Align(
              alignment: Alignment.topRight,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: IconButton(
                  onPressed: widget.onClose,
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
              ),
            ),

            // 数字人头像和对话框 - 水平布局
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    // 数字人头像（左侧）
                    Expanded(
                      flex: 2,
                      child: Center(
                        child: AnimatedBuilder(
                          animation: _avatarAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: _avatarAnimation.value,
                              child: _buildAvatar(),
                            );
                          },
                        ),
                      ),
                    ),

                    const SizedBox(width: 20),

                    // 对话框（右侧）
                    Expanded(
                      flex: 3,
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.3),
                              blurRadius: 15,
                              offset: const Offset(0, 8),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            // 文字播报区域
                            Expanded(
                              child: Center(
                                child: AnimatedBuilder(
                                  animation: _textAnimation,
                                  builder: (context, child) {
                                    return Opacity(
                                      opacity: _textAnimation.value,
                                      child: Text(
                                        _currentText,
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                          height: 1.5,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),

                            // 预测号码展示
                            if (_ballController.isAnimating || _ballController.isCompleted)
                              _buildNumberDisplay(),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),
          ],
        ),
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 200,
      height: 200,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.cyan.shade300,
            Colors.blue.shade400,
            Colors.purple.shade400,
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.white.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 头部
          Positioned(
            top: 30,
            left: 50,
            right: 50,
            child: Container(
              height: 80,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(40),
              ),
            ),
          ),
          
          // 眼睛
          Positioned(
            top: 50,
            left: 70,
            child: _buildEye(),
          ),
          Positioned(
            top: 50,
            right: 70,
            child: _buildEye(),
          ),
          
          // 嘴巴
          Positioned(
            top: 90,
            left: 80,
            right: 80,
            child: Container(
              height: 20,
              decoration: BoxDecoration(
                color: Colors.pink.shade300,
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
          
          // 装饰元素
          Positioned(
            top: 10,
            right: 20,
            child: Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                color: Colors.yellow.shade400,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.auto_awesome,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEye() {
    return Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        color: Colors.black87,
        shape: BoxShape.circle,
      ),
      child: Container(
        margin: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  Widget _buildNumberDisplay() {
    return AnimatedBuilder(
      animation: _ballAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _ballAnimation.value,
          child: Column(
            children: [
              const Divider(),
              const SizedBox(height: 10),
              
              // 号码球展示
              if (widget.prediction.lotteryType?.code == 'fc3d')
                _buildFC3DDisplay()
              else
                _buildRegularDisplay(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFC3DDisplay() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: widget.prediction.redBallsList.map((ball) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.green.shade600,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              ball,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildRegularDisplay() {
    return Column(
      children: [
        // 红球/前区
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ...widget.prediction.redBallsList.map((ball) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 2),
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: Colors.red.shade600,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    ball,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  ),
                ),
              );
            }),
            
            // 分隔符
            if (widget.prediction.blueBallsList.isNotEmpty) ...[
              const SizedBox(width: 8),
              Container(
                width: 1,
                height: 16,
                color: Colors.grey.shade400,
              ),
              const SizedBox(width: 8),
            ],
            
            // 蓝球/后区
            ...widget.prediction.blueBallsList.map((ball) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 2),
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: Colors.blue.shade600,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    ball,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  ),
                ),
              );
            }),
          ],
        ),
      ],
    );
  }
}
